/* Global Styles with Theme Variables */

/* Database Error Screen */
.db-error-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--bg-primary);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.db-error-screen.active {
  opacity: 1;
  visibility: visible;
}

.db-error-content {
  background-color: var(--bg-secondary);
  border-radius: 8px;
  padding: 30px;
  text-align: center;
  max-width: 500px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid var(--border-color);
}

.db-error-icon {
  font-size: 60px;
  color: var(--accent-primary);
  margin-bottom: 20px;
  position: relative;
  display: inline-block;
}

.db-error-x {
  position: absolute;
  font-size: 30px;
  color: #ff4d4d;
  bottom: 0;
  right: -15px;
}

.db-error-content h2 {
  margin-bottom: 15px;
  color: var(--text-primary);
}

.db-error-content p {
  margin-bottom: 25px;
  color: var(--text-secondary);
}

#retry-connection {
  padding: 10px 20px;
  font-size: 16px;
}

:root {
  /* Dark Theme (VSCode) - Default */
  --bg-primary: #1e1e1e;
  --bg-primary-rgb: 30, 30, 30;
  --bg-secondary: #252526;
  --bg-secondary-rgb: 37, 37, 38;
  --bg-tertiary: #333333;
  --bg-tertiary-rgb: 51, 51, 51;
  --text-primary: #cccccc;
  --text-primary-rgb: 204, 204, 204;
  --text-secondary: #a0a0a0;
  --accent-primary: #1e90ff; /* dodgerblue */
  --accent-primary-rgb: 30, 144, 255; /* dodgerblue RGB values */
  --accent-secondary: #39a0ff; /* lighter dodgerblue */
  --border-color: #333333;
  --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
  --hover-bg: #2d2d2d;
  --progress: 0%; /* Default progress value, overridden dynamically by JavaScript */
}

/* Light Theme */
body.light-theme {
  --bg-primary: #f5f7fa;
  --bg-primary-rgb: 245, 247, 250;
  --bg-secondary: #ffffff;
  --bg-secondary-rgb: 255, 255, 255;
  --bg-tertiary: #f2f2f2;
  --bg-tertiary-rgb: 242, 242, 242;
  --text-primary: #333333;
  --text-primary-rgb: 51, 51, 51;
  --text-secondary: #7f8c8d;
  --accent-primary: #1e90ff; /* dodgerblue */
  --accent-primary-rgb: 30, 144, 255; /* dodgerblue RGB values */
  --accent-secondary: #0077ea; /* darker dodgerblue for light theme */
  --border-color: #e0e0e0;
  --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --hover-bg: #f5f5f5;
  --progress: 0%; /* Default progress value, overridden dynamically by JavaScript */
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
}

/* Custom scrollbar for Chrome, Safari and Opera */
::-webkit-scrollbar {
  width: 3px; /* Reduced width of the vertical scrollbar from 6px to 3px */
  height: 0; /* Hide horizontal scrollbar */
}

::-webkit-scrollbar-track {
  background: transparent; /* Track background */
}

::-webkit-scrollbar-thumb {
  background: var(--accent-primary); /* Scrollbar color (dodgerblue) */
  border-radius: 1.5px; /* Reduced rounded corners */
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-secondary); /* Lighter dodgerblue on hover */
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin; /* "auto" or "thin" */
  scrollbar-color: var(--accent-primary) transparent; /* thumb and track color */
}

/* Hide horizontal scrollbar in Firefox */
html, body {
  overflow-x: hidden;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.app-container {
  display: flex;
  min-height: 100vh;
  width: 100vw;
  padding: 0;
  margin: 0;
  position: relative;
  z-index: 10; /* Ensure it's above the map */
  background-color: transparent; /* Make it transparent */
  max-width: 100vw !important; /* Prevent horizontal overflow */
}

/* Lateral Tabs Styles */
.lateral-tabs {
  position: fixed;
  left: 10px; /* Detached from the left side */
  top: 50%;
  transform: translateY(-50%);
  width: 50px; /* Reduced width */
  background-color: rgba(30, 30, 30, 0.7); /* Semi-transparent background */
  border-radius: 15px; /* Fully rounded */
  padding: 15px 0; /* Reduced padding */
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px; /* Reduced gap */
  z-index: 1000; /* Higher z-index to ensure it's above the map */
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.3); /* Shadow all around */
  transition: all 0.3s ease;
  backdrop-filter: blur(5px); /* Add blur effect for modern browsers */
  border: 1px solid rgba(255, 255, 255, 0.1); /* Subtle border */
}

/* Collapsed state */
.lateral-tabs.collapsed {
  width: 0;
  padding: 0;
  left: 0;
  background-color: transparent;
  box-shadow: none;
}

.lateral-tabs.collapsed .tab {
  transform: scale(0);
  width: 0;
  height: 0;
  margin-left: -50px;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

.lateral-tabs.collapsed .tab-tooltip {
  display: none;
}

.lateral-tabs.collapsed .tabs-toggle {
  transform: rotate(180deg); /* Just rotate */
  left: 5px; /* Adjusted position */
  top: 50%; /* Center vertically */
  margin-top: -10px; /* Half of the height to center perfectly */
  background-color: var(--accent-primary);
  border-color: var(--accent-primary);
}

.lateral-tabs.collapsed .tabs-toggle i {
  color: white;
}

.tab {
  width: 35px; /* Reduced size */
  height: 35px; /* Reduced size */
  border-radius: 50%;
  background-color: var(--bg-tertiary);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.1); /* Subtle border */
}

.tab i {
  color: var(--text-primary);
  font-size: 16px; /* Smaller icon */
  transition: all 0.3s ease;
}

.tab:hover {
  background-color: var(--accent-primary);
  transform: scale(1.1);
}

.tab.active {
  background-color: var(--accent-primary);
}

.tab-tooltip {
  position: absolute;
  left: 60px;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  padding: 5px 10px;
  border-radius: 5px;
  font-size: 14px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  white-space: nowrap;
  border: 1px solid var(--border-color);
}

.tab:hover .tab-tooltip {
  opacity: 1;
  visibility: visible;
}

/* Tabs Toggle Button */
.tabs-toggle {
  position: absolute;
  right: -10px; /* Adjusted position */
  top: 50%;
  transform: translateY(-50%);
  width: 20px; /* Smaller size */
  height: 20px; /* Smaller size */
  background-color: var(--bg-secondary);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease, left 0.3s ease, right 0.3s ease;
  border: 1px solid var(--border-color);
  z-index: 101;
}

.tabs-toggle:hover {
  background-color: var(--accent-primary);
}

.tabs-toggle i {
  color: var(--text-primary);
  font-size: 10px; /* Smaller icon */
  transition: color 0.3s ease;
}

.tabs-toggle:hover i {
  color: #ffffff;
}

/* Content Area Styles */
.content-area {
  flex: 1;
  margin-left: 80px; /* Adjusted for detached sidebar */
  margin-right: 20px; /* Add right margin for symmetry */
  padding: 20px 0 20px 30px; /* Increased left padding */
  min-height: calc(100vh - 40px); /* Minimum height */
  transition: margin-left 0.3s ease, margin-right 0.3s ease;
  position: relative;
  z-index: 10; /* Ensure it's above the map */
  background-color: transparent; /* Make it transparent */
  pointer-events: auto; /* Ensure interactions work */
  max-width: calc(100vw - 100px) !important; /* Adjusted for detached sidebar */
  display: flex;
  flex-direction: column;
}

/* Only allow scrolling on active tab content */
.tab-content.active {
  overflow-y: auto;
  overflow-x: hidden;
}



/* Adjust content area when tabs are collapsed */
.tabs-collapsed .content-area {
  margin-left: 40px; /* Adjusted for detached sidebar */
}

.tab-content {
  display: none;
  animation: fadeIn 0.5s ease;
  position: absolute;
  width: 100%; /* Full width */
  opacity: 0;
  transition: opacity 0.5s ease, transform 0.5s ease;
  padding-top: 20px; /* Reduced from 30px */
  overflow: hidden; /* Hide overflow during transitions */
  min-height: calc(100vh - 100px); /* Minimum height to fit in viewport */
}

.tab-content.active {
  display: flex !important;
  flex-direction: column;
  opacity: 1;
  position: relative;
  z-index: 2; /* Ensure active tab is above others */
  overflow-y: auto; /* Allow vertical scrolling for active tab */
  overflow-x: hidden; /* Prevent horizontal scrolling */
  min-height: 100%; /* Take full height of parent */
}

/* Prevent vertical scrolling for all tab contents */
#dashboard-content.tab-content.active,
#map-content.tab-content.active,
#list-content.tab-content.active,
#actions-content.tab-content.active {
  overflow-y: hidden !important;
  height: calc(100vh - 80px) !important; /* Fixed height to prevent overflow */
  max-height: calc(100vh - 80px) !important;
}

/* Ensure the content area doesn't overflow when any tab is active */
.content-area:has(.tab-content.active) {
  overflow: hidden !important;
  height: calc(100vh - 40px) !important;
  max-height: calc(100vh - 40px) !important;
}

/* Allow internal scrolling only for specific containers */
.dashboard-borderpane,
.map-container,
.split-container,
.actions-container {
  overflow-y: auto !important;
  max-height: calc(100vh - 120px) !important;
}

/* Add bottom margin to dashboard content */
#dashboard-content {
  margin-bottom: 20px;
}

/* iPhone-style Toggle Buttons Container */
.dashboard-toggle-container {
  position: absolute;
  top: 5px;
  right: 120px; /* Position next to the title */
  z-index: 11; /* Above the title */
}

.dashboard-toggle-buttons {
  position: relative;
  display: flex;
  background-color: rgba(60, 60, 60, 0.8);
  border-radius: 5px;
  padding: 2px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Sliding background indicator */
.dashboard-toggle-buttons .toggle-slider {
  position: absolute;
  top: 2px;
  left: 2px;
  height: calc(100% - 4px);
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  border-radius: 5px;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(30, 144, 255, 0.4);
  z-index: 1;
  width: 60px; /* Default width for first button */
}

/* Individual toggle buttons */
.dashboard-toggle-btn {
  position: relative;
  background: none;
  border: none;
  padding: 4px 8px;
  font-size: 10px;
  font-weight: 500;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 5px;
  white-space: nowrap;
  z-index: 2;
  min-width: 60px;
  text-align: center;
}

.dashboard-toggle-btn.active {
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.dashboard-toggle-btn:hover:not(.active) {
  color: var(--text-primary);
  background-color: rgba(255, 255, 255, 0.1);
}

/* Dashboard main view - two sections sharing height */
.dashboard-main-view {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 10px;
  margin-top: 40px; /* Restored spacing since toggle buttons are now absolute */
}

/* Dashboard KPI view - hidden by default, full page when shown */
.dashboard-kpi-view {
  display: none;
  flex-direction: column;
  flex: 1;
  gap: 10px;
  margin-top: 40px; /* Add top spacing like other pages */
  height: calc(100vh - 80px); /* Full height minus margins */
  overflow-y: auto; /* Allow scrolling if content overflows */
}

/* Dashboard Trainee KPI view - hidden by default, full page when shown */
.dashboard-trainee-kpi-view {
  display: none;
  flex-direction: column;
  flex: 1;
  gap: 10px;
  margin-top: 40px; /* Add top spacing like other pages */
  height: calc(100vh - 80px); /* Full height minus margins */
  overflow-y: auto; /* Allow scrolling if content overflows */
}

/* KPI container should adapt like a tile/flow pane */
.dashboard-kpi-view .kpi-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  height: 100%;
}

/* KPI row should wrap and adapt to content */
.dashboard-kpi-view .kpi-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: flex-start;
  justify-content: flex-start;
  flex: 1;
}

/* Individual KPI items should be flexible */
.dashboard-kpi-view .kpi-item {
  flex: 1 1 300px; /* Flexible with minimum width */
  min-height: 200px;
  max-width: 400px;
}

/* Full page training statistics */
.kpi-training-fullpage {
  width: 100%;
  height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 0;
  overflow: hidden;
}

/* Domain selection rectangles container */
.domain-selection-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  margin: 20px 0 15px 0;
  flex-shrink: 0;
  padding: 0 20px;
  flex-wrap: wrap;
}

/* Domain rectangles */
.domain-circle {
  flex: 1;
  height: 45px;
  border-radius: 10px;
  background: linear-gradient(135deg, var(--bg-tertiary), rgba(var(--bg-tertiary-rgb), 0.8));
  border: 2px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  padding: 0 12px;
}

.domain-circle:hover {
  border-color: var(--accent-primary);
  box-shadow: 0 5px 20px rgba(30, 144, 255, 0.3);
  background: linear-gradient(135deg, rgba(var(--bg-tertiary-rgb), 0.9), rgba(var(--bg-tertiary-rgb), 0.7));
}

.domain-circle.active {
  background: linear-gradient(135deg, var(--accent-primary), #4169E1);
  border-color: var(--accent-primary);
  color: white;
  box-shadow: 0 6px 25px rgba(30, 144, 255, 0.5);
}

.domain-circle span {
  font-size: 13px;
  font-weight: 600;
  text-align: center;
  color: inherit;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Training statistics container */
.training-stats-container {
  width: 100%;
  display: flex;
  flex: 1;
  gap: 20px;
  height: calc(100% - 80px);
  min-height: 0;
  padding: 0 20px 15px 20px;
}

/* Left section - Evolution chart */
.training-stats-left {
  flex: 1;
  width: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, rgba(var(--bg-tertiary-rgb), 0.6), rgba(var(--bg-tertiary-rgb), 0.3));
  border-radius: 20px;
  padding: 25px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-height: 0;
}

/* Right section - Count and breakdown */
.training-stats-right {
  flex: 1;
  width: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, rgba(var(--bg-tertiary-rgb), 0.6), rgba(var(--bg-tertiary-rgb), 0.3));
  border-radius: 20px;
  padding: 25px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-height: 0;
}

/* Evolution section styles */
.evolution-section {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.evolution-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
  margin-bottom: 20px;
  background: linear-gradient(135deg, var(--accent-primary), #4169E1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.evolution-chart-container {
  flex: 1;
  position: relative;
  min-height: 200px;
  margin-bottom: 15px;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 15px;
  padding: 15px;
}

.evolution-percentage {
  font-size: 36px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 8px;
  background: linear-gradient(135deg, var(--accent-primary), #4169E1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.evolution-label {
  font-size: 14px;
  color: var(--text-secondary);
  text-align: center;
  font-weight: 500;
}

.evolution-details {
  margin-top: 15px;
  padding: 15px;
  background: rgba(var(--accent-primary-rgb), 0.1);
  border-radius: 12px;
  border-left: 4px solid var(--accent-primary);
}

.evolution-details-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.evolution-details-text {
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* Training count section styles */
.training-count-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 80%;
  max-height: 500px; /* Fixed height for statistics */
}

/* Top section with count and formations side by side */
.training-count-header {
  width: 100%;
  display: flex;
  align-items: flex-start;
  gap: 30px;
  margin-bottom: 25px;
  flex-shrink: 0;
}

.training-count-left {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
}

.training-total-count {
  font-size: 80px;
  font-weight: bold;
  color: var(--text-primary);
  text-align: center;
  margin-bottom: 10px;
  line-height: 1;
  background: linear-gradient(135deg, var(--accent-primary), #4169E1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.training-total-label {
  font-size: 18px;
  color: var(--text-secondary);
  text-align: center;
  font-weight: 500;
  white-space: nowrap;
}

/* Top unités section */
.top-unites-section {
  flex: 1;
  min-width: 200px;
}

.top-unites-title {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.top-unites-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.unite-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border-left: 3px solid var(--accent-primary);
  transition: all 0.3s ease;
}

.unite-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(2px);
}

.unite-name {
  font-size: 11px;
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: 0.3px;
}

.unite-count {
  font-size: 11px;
  font-weight: 700;
  color: var(--accent-primary);
  background: rgba(30, 144, 255, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  min-width: 30px;
  text-align: center;
}

/* Formations list on the right */
.training-count-right {
  flex: 1;
  width: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.formations-list-title {
  width: 100%;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 10px;
  padding-left: 4px;
}

.formations-list-title.clickable-title {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 4px;
  padding: 6px 8px;
  margin-left: -4px;
  position: relative;
}

.formations-list-title.clickable-title:hover {
  color: var(--accent-primary);
  background: rgba(var(--accent-primary-rgb), 0.1);
  transform: translateX(2px);
}

.formations-list-title.clickable-title:active {
  transform: translateX(1px);
  background: rgba(var(--accent-primary-rgb), 0.2);
}

.formations-list {
  width: 100%;
  flex: 1;
  max-height: 180px; /* Increased height */
  overflow-y: auto;
  border-radius: 6px; /* Reduced border radius */
  background: transparent; /* Removed background */
  padding: 5px; /* Reduced padding */
  border: none; /* Removed border */
}

.formation-item {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 8px 8px; /* Slightly increased padding for separator space */
  margin-bottom: 0; /* Removed margin for separator */
  background: transparent; /* Removed background */
  border-radius: 6px; /* Reduced border radius */
  border-left: none; /* Removed blue border */
  border-bottom: 1px solid rgba(var(--text-primary-rgb), 0.4); /* Added horizontal separator */
  transition: all 0.3s ease;
  backdrop-filter: none; /* Removed blur effect */
}

.formation-item:last-child {
  border-bottom: none; /* Remove separator from last item */
  margin-bottom: 0;
}

.formation-item:hover {
  background: rgba(var(--bg-tertiary-rgb), 0.3); /* Subtle hover effect */
  transform: none; /* Removed transform */
  box-shadow: none; /* Removed shadow */
}

.formation-name {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-primary);
  flex: 1;
  margin-right: 10px;
  line-height: 1.3;
}

.formation-time {
  font-size: 10px;
  color: var(--text-secondary);
  white-space: nowrap;
  font-weight: 400;
  background: rgba(var(--accent-primary-rgb), 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

.training-summary {
  width: 100%;
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(var(--accent-primary-rgb), 0.1);
  border-radius: 12px;
  border-left: 4px solid var(--accent-primary);
  flex-shrink: 0;
}

.training-summary-title {
  width: 100%;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.training-summary-text {
  width: 100%;
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* Training types breakdown */
.training-types-breakdown {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 4px; /* Further reduced gap for tighter spacing */
  flex: 1;
  overflow-y: auto;
  min-height: 120px; /* Added minimum height */
}

.training-type-item {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px; /* Reduced padding slightly */
  background: linear-gradient(135deg, rgba(var(--bg-secondary-rgb), 0.8), rgba(var(--bg-secondary-rgb), 0.6));
  border-radius: 8px; /* Reduced border radius */
  border-left: none; /* Removed blue border */
  border-bottom: 1px solid rgba(var(--text-primary-rgb), 0.4); /* Added horizontal separator */
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  min-height: 45px; /* Slightly reduced minimum height */
  margin-bottom: 0; /* Removed margin for separator */
}

.training-type-item:last-child {
  border-bottom: none; /* Remove separator from last item */
}

.training-type-item:hover {
  transform: translateX(5px);
  box-shadow: 0 4px 15px rgba(30, 144, 255, 0.2);
}

.training-type-name {
  font-size: 14px;
  color: var(--text-primary);
  font-weight: 500;
}

.training-type-count {
  font-size: 18px;
  color: var(--accent-primary);
  font-weight: bold;
  background: linear-gradient(135deg, var(--accent-primary), #4169E1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Special styling for map tab content */
#map-content {
  padding: 20px !important;
  padding-bottom: 20px !important; /* Added bottom padding */
  position: relative !important;
  z-index: 10 !important; /* Higher z-index to appear above the map */
  overflow: hidden !important; /* Force hidden overflow for map tab */
}

/* Map container should allow internal scrolling if needed */
.map-container {
  overflow-y: auto !important; /* Allow internal scrolling */
  overflow-x: hidden !important; /* Prevent horizontal scrolling */
  max-height: calc(100vh - 120px) !important; /* Ensure it fits within viewport */
}

/* Remove custom styling for map title - it will use the common h1 styling */

/* Enhanced animations for tab transitions */
.tab-content.slide-left {
  transform: translateX(-50px);
}

.tab-content.slide-right {
  transform: translateX(50px);
}

.tab-content.active.slide-in {
  transform: translateX(0);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

h1 {
  font-size: 12px;
  color: var(--text-secondary); /* Use theme variable for better compatibility */
  position: absolute;
  top: 5px; /* Reduced from 10px */
  right: 5px;
  margin: 0 0 20px 0; /* Further increased bottom margin */
  padding: 5px 10px;
  font-weight: normal;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  z-index: 10; /* Ensure it's above other elements */
  background-color: var(--bg-secondary); /* Use theme variable */
  border-radius: 4px;
  box-shadow: var(--card-shadow); /* Use theme variable */
  border: 1px solid var(--border-color); /* Use theme variable */
}

/* Content spacing after title */
.dashboard-grid,
.map-container,
.list-container,
.actions-container {
  margin-top: 20px; /* Increased space between title and content */
}

/* Current year display */
.current-year-display {
  font-size: 24px;
  font-weight: bold;
  color: var(--text-primary);
  z-index: 10;
  background-color: transparent;
  padding: 5px 10px;
  border-radius: 4px;
}

/* Planning Schedule styles */
.planning-schedule-container {
  width: 100%;
  overflow: visible; /* Changed from hidden to visible to allow content to expand */
  margin-top: 20px; /* Reduced to 1px as requested */
  margin-bottom: 20px;
  background-color: var(--bg-secondary);
  border-radius: 8px;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
  padding: 1px 10px 2px 10px; /* Reduced top padding to 1px */
  position: relative; /* For positioning elements */
  transition: all 0.5s ease; /* Smooth transition when height changes */
}

/* Container for the control buttons */
.timeline-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1px 0;
  margin-bottom: 1px;
  width: 100%;
}

/* Timeline sections */
.timeline-left, .timeline-center, .timeline-right {
  display: flex;
  align-items: center;
}

.timeline-center {
  justify-content: center;
  flex: 1;
}

.timeline-right {
  justify-content: flex-end;
  gap: 10px; /* Space between buttons */
}

/* Drag indicator */
.drag-indicator {
  display: flex;
  align-items: center;
  gap: 5px;
  color: var(--text-primary);
  opacity: 0.5; /* Increased opacity for better visibility */
  z-index: 5;
  pointer-events: none; /* Prevent it from interfering with interactions */
}

.drag-indicator .fa-hand-pointer {
  font-size: 20px;
  transform: rotate(0deg); /* No rotation - pointing up by default */
}

.drag-indicator .fa-arrow-left,
.drag-indicator .fa-arrow-right {
  font-size: 16px;
}

/* Timeline control buttons */
.timeline-btn {
  display: flex;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 50%; /* Make it circular */
  width: 30px;
  height: 30px;
  font-size: 14px;
  cursor: pointer;
  z-index: 10;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.timeline-btn:hover {
  background-color: var(--accent-primary);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
}

.timeline-btn i {
  font-size: 14px;
  transition: transform 0.5s ease;
}

/* Refresh animation */
.refreshing i {
  animation: spin 1s linear;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Button styles - specific positioning handled by the container in timeline-right */

.planning-schedule {
  display: flex;
  flex-direction: column;
  overflow-x: auto;
  scroll-behavior: auto; /* Changed from 'smooth' to 'auto' to allow our custom momentum scrolling */
  cursor: grab;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  position: relative;
  min-height: 320px; /* Increased minimum height to accommodate larger timeline events */
  overflow-y: hidden; /* Prevent vertical scrolling */
  touch-action: pan-x; /* Optimize for horizontal touch gestures */
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  will-change: scroll-position; /* Optimize for scrolling performance */
}

.planning-schedule:active {
  cursor: grabbing;
}

/* Hide scrollbar but keep functionality */
.planning-schedule::-webkit-scrollbar {
  height: 0; /* Hide scrollbar */
  width: 0;
  display: none; /* For Firefox */
}

/* For Firefox */
.planning-schedule {
  scrollbar-width: none;
}

/* For IE and Edge */
.planning-schedule {
  -ms-overflow-style: none;
}

.timeline-container {
  position: relative;
  width: max-content;
  min-width: 100%;
  padding-bottom: 25px; /* Add padding to make room for the count text */
}

.timeline-weeks {
  display: flex;
  position: relative;
  margin-bottom: 10px;
}

.week-header {
  text-align: center;
  font-weight: bold;
  color: var(--accent-primary);
  padding: 5px 0;
  position: relative;
  width: 325px; /* Width for a week (5 days × 65px) */
  border-right: 2px solid var(--accent-primary);
}

/* Current week highlight - yellow text */
.week-header.current-week {
  color: #FFD700; /* Gold/yellow text for current week */
  background-color: transparent; /* No background color */
  font-weight: bold;
}

.timeline-days {
  display: flex;
  position: relative;
  height: 50px;
  margin-bottom: 5px;
}

.day-column {
  width: 65px; /* Further reduced from 70px to 65px */
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-right: 1px solid var(--border-color);
}

/* Current day highlight - yellow text */
.day-column.current-day .day-name,
.day-column.current-day .day-date {
  color: #FFD700; /* Gold/yellow text for current day */
  font-weight: bold;
}

.day-column:nth-child(5n) {
  border-right: 2px solid var(--accent-primary);
}

.day-name {
  font-weight: bold;
  color: var(--text-primary);
  font-size: 14px; /* Increased from 12px */
  margin-bottom: 4px;
}

.day-date {
  color: var(--text-secondary);
  font-size: 13px; /* Increased from 11px */
}

.timeline-line {
  position: relative;
  height: 3px; /* Increased from 2px for better visibility */
  background-color: var(--text-primary);
  width: 100%;
  margin: 10px 0;
}

.timeline-tick {
  position: absolute;
  width: 1px;
  height: 10px;
  background-color: var(--text-primary);
  bottom: 0;
  transform: translateX(-50%);
}

.timeline-tick.day-start {
  height: 15px;
  width: 2px;
}

.timeline-tick.week-boundary {
  height: 20px; /* Reduced height from 30px to 20px */
  width: 2px;
  background-color: var(--accent-primary);
}

/* Yellow tick for current day */
.timeline-tick.current-day {
  background-color: #FFD700; /* Gold/yellow for current day */
}

.timeline-events {
  position: relative;
  width: 100%;
  max-height: 250px; /* Increased height for more training slots */
  overflow-y: auto; /* Enable vertical scrolling */
  overflow-x: hidden;
  padding-right: 5px; /* Space for scrollbar */
}

/* Custom scrollbar for timeline events */
.timeline-events::-webkit-scrollbar {
  width: 6px;
}

.timeline-events::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.timeline-events::-webkit-scrollbar-thumb {
  background: var(--accent-primary);
  border-radius: 3px;
  opacity: 0.7;
}

.timeline-events::-webkit-scrollbar-thumb:hover {
  background: var(--accent-primary);
  opacity: 1;
}

.event-row {
  height: 30px;
  background-color: rgba(255, 255, 255, 0.1);
  margin-bottom: 6px; /* Reduced from 10px to 6px */
  border-radius: 4px;
  border: 1px solid var(--border-color);
  position: relative; /* For positioning training events */
}

/* When there's only one row, make it slightly taller */
.timeline-events:only-child .event-row:only-child {
  height: 40px;
}

/* When there are multiple rows, adjust spacing */
.timeline-events .event-row:last-child {
  margin-bottom: 0;
}

/* Active trainings count text */
.active-trainings-count {
  position: absolute;
  bottom: 8px;
  right: 10px;
  color: #333333; /* Light mode text color (--text-primary in light theme) */
  font-size: 11px; /* Smaller font size */
  font-weight: 500; /* Medium weight */
  font-style: italic;
  z-index: 9999; /* Very high z-index to ensure visibility */
  background-color: #e0e0e0; /* Light grey background */
  padding: 1px 8px; /* Slightly less padding */
  border-radius: 20px; /* Fully rounded corners */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); /* Subtle shadow */
  pointer-events: none; /* Prevent it from blocking interactions */
}



/* Activities Timeline styles */
/* Today marker styles */
.today-marker {
  position: absolute;
  width: 14px;
  height: 14px;
  background-color: var(--accent-primary);
  border-radius: 50%;
  top: -7px;
  transform: translateX(-50%);
  z-index: 6;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
  animation: pulse 2s infinite;
  display: flex;
  flex-direction: column;
  align-items: center;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(30, 144, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(30, 144, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(30, 144, 255, 0);
  }
}

.today-marker::after {
  content: '';
  position: absolute;
  width: 28px;
  height: 28px;
  border: 2px solid var(--accent-primary);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0.3;
}

.today-date {
  position: absolute;
  top: -40px; /* Adjusted position */
  left: 50%;
  transform: translateX(-50%);
  font-size: 11px; /* Slightly smaller to fit day name */
  font-weight: bold;
  color: white;
  text-align: center;
  background-color: var(--accent-primary);
  padding: 3px 8px;
  border-radius: 12px;
  z-index: 6;
  width: max-content;
  pointer-events: none; /* Prevent it from blocking hover on the marker */
  white-space: nowrap; /* Prevent wrapping */
  margin-top: 0; /* Removed margin to maintain exactly 1px between labels */
}

/* Vertical line for today marker - styled like future activities but with accent color */
.today-vertical-line {
  position: absolute;
  width: 2px; /* Increased stroke width */
  background-color: var(--accent-primary); /* Keep accent color */
  top: 0; /* Start from the center of the circle */
  left: 0;
  height: 60px; /* Same height as activity-vertical-line */
  opacity: 0.7; /* Same opacity as upcoming activity vertical line */
  transform: translateY(7px); /* Adjust to start from center of circle (today marker is 14px) */
  box-shadow: 0 0 1px rgba(var(--accent-primary-rgb, 30, 144, 255), 0.3); /* Subtle shadow like activity-vertical-line */
}

/* Today's activity message - styled like future activities */
.today-activity {
  position: absolute;
  top: 18px; /* Match the top position of activity-names-container */
  left: 12px; /* Position to the right of the vertical line */
  font-size: 13px; /* Increased from 11px */
  color: var(--accent-primary); /* Keep accent color for today's activity */
  text-align: left;
  font-weight: bold; /* Match the upcoming activity style */
  display: flex;
  align-items: center;
  gap: 5px;
  width: 200px; /* Match the width of activity-names-container */
  max-width: 200px; /* Match the max-width of activity-names-container */
  pointer-events: none;
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3; /* Show 3 lines instead of 2 */
  -webkit-box-orient: vertical;
  line-height: 1.3; /* Better line spacing */
  margin-bottom: 5px; /* Add spacing between multiple activities */
}

.today-activity i {
  color: var(--accent-primary);
  font-size: 16px; /* Increased from 14px */
  flex-shrink: 0; /* Prevent icon from shrinking */
}

/* Removed pulse-line animation as it's no longer needed */

.today-label {
  position: absolute;
  top: -540px; /* Fine-tuned to maintain exactly 1px margin */
  left: 50%;
  transform: translateX(-50%);
  font-size: 11px; /* Reduced font size as requested */
  color: var(--accent-primary);
  text-align: center;
  font-weight: bold;
  background-color: transparent; /* Removed background color */
  padding: 0; /* Removed padding */
  width: auto;
  white-space: nowrap;
  pointer-events: none; /* Prevent it from blocking hover on the marker */
  z-index: 7; /* Higher than the date label to ensure it appears on top */
  text-shadow: 0 0 1px rgba(255, 255, 255, 0.5); /* Subtle text shadow for better visibility */
  display: flex; /* Added to make arrow inline with text */
  align-items: center; /* Center items vertically */
  gap: 3px; /* Small gap between text and arrow */
  line-height: 1; /* Ensure consistent height */
}
.activities-timeline-container {
  width: 100%;
  overflow: hidden;
  margin-top: 8px; /* Further increased top margin to accommodate the Aujourd'hui label */
  margin-bottom: 20px;
  padding: 0;
  position: relative;
  height: 140px; /* Further increased height to ensure the Aujourd'hui label is fully visible */
}

/* Activities drag indicator */
.activities-drag-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 4;
  opacity: 0.3; /* Reduced opacity as requested */
  pointer-events: none;
  color: var(--text-secondary);
  font-size: 14px;
  letter-spacing: 5px;
  background-color: rgba(0, 0, 0, 0.1);
  padding: 5px 10px;
  border-radius: 15px;
}

/* Title removed */

.activities-timeline {
  width: 100%;
  height: 100%;
  overflow-x: auto;
  white-space: nowrap;
  position: relative;
  user-select: none; /* Prevent text selection during drag */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Hide scrollbar completely but keep functionality */
.activities-timeline::-webkit-scrollbar {
  height: 0;
  width: 0;
  display: none;
}

.activities-timeline {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.activities-timeline-line {
  position: absolute; /* Changed from relative to absolute */
  height: 1px; /* Thinner line */
  background-color: rgba(var(--text-primary-rgb, 200, 200, 200), 0.4); /* More transparent */
  width: 100%; /* Will be set dynamically in JavaScript */
  margin: 0;
  top: 65%; /* Further adjusted to be lower in the container */
  left: 0; /* Start from the left edge */
  transform: translateY(-50%); /* Perfect vertical centering */
}

.activity-point {
  position: absolute;
  width: 16px; /* Increased from 12px */
  height: 16px; /* Increased from 12px */
  background-color: var(--text-primary);
  border-radius: 50%;
  top: -8px; /* Adjusted to center on the line */
  transform: translateX(-50%);
  cursor: pointer;
  z-index: 5;
  display: flex;
  flex-direction: column;
  align-items: center; /* Center children horizontally */
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

/* Today's activity point - styled like the today marker */
.activity-point.today-activity-point {
  width: 18px; /* Increased from 14px */
  height: 18px; /* Increased from 14px */
  background-color: var(--accent-primary);
  top: -9px; /* Adjusted to center on the line */
  z-index: 6; /* Higher z-index to appear above other points */
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
  animation: pulse 2s infinite;
}

/* Add the same after effect as the today marker */
.activity-point.today-activity-point::after {
  content: '';
  position: absolute;
  width: 28px;
  height: 28px;
  border: 2px solid var(--accent-primary);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0.3;
}

/* Add down arrow for today's activity point */
.activity-point.today-activity-point::before {
  content: '▼';
  position: absolute;
  color: var(--accent-primary);
  font-size: 12px;
  top: -25px; /* Position above the point */
  left: 50%;
  transform: translateX(-50%);
  opacity: 0.8;
}

/* Subtle hover effect for activity points (no zoom) */
.activity-point:hover {
  transform: translateX(-50%); /* No scale */
  box-shadow: 0 0 8px rgba(var(--text-primary-rgb, 200, 200, 200), 0.6);
  z-index: 10;
}

/* Multiple activities point styling */
.activity-point.multiple-activities {
  background-color: var(--accent-primary);
  box-shadow: 0 0 0 2px rgba(var(--accent-primary-rgb, 30, 144, 255), 0.3);
}

/* Activity dots navigation */
.activity-dots {
  position: absolute;
  display: flex;
  flex-direction: column;
  gap: 8px;
  left: -15px; /* Position to the left of the vertical line with margin */
  top: 20px; /* Start below the circle */
  z-index: 5;
  align-items: center; /* Center dots horizontally */
  padding-top: 5px; /* Add padding for the more indicator */
  padding-bottom: 5px; /* Add padding for the more indicator */
}

/* More dots indicators */
.activity-dots::before,
.activity-dots::after {
  content: '';
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 5px; /* Slightly smaller */
  height: 5px; /* Slightly smaller */
  border-radius: 50%;
  background-color: var(--text-secondary);
  opacity: 0;
  transition: opacity 0.2s ease;
}

/* Top more indicator (for dots above) */
.activity-dots::before {
  top: -3px;
}

/* Bottom more indicator (for dots below) */
.activity-dots::after {
  bottom: -3px;
}

/* Show top indicator when there are hidden dots above */
.activity-dots.has-more-above::before {
  opacity: 0.4; /* Reduced opacity as requested */
}

/* Show bottom indicator when there are hidden dots below */
.activity-dots.has-more-below::after {
  opacity: 0.4; /* Reduced opacity as requested */
}

/* Individual activity dot */
.activity-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--text-secondary);
  opacity: 0.5;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  align-items: center;
}

/* Dot number - positioned to the left of the dot */
.activity-dot::after {
  content: attr(data-number);
  position: absolute;
  font-size: 10px; /* Increased from 8px */
  color: var(--text-secondary);
  opacity: 0.8; /* Increased from 0.7 for better visibility */
  right: 15px; /* Position to the left of the dot with more space */
  left: auto;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  text-align: right; /* Align text to the right (closer to the dot) */
  min-width: 10px; /* Ensure consistent spacing */
  font-weight: 600; /* Added for better visibility */
}

/* For past activities, position the number on the right */
.activity-point.past .activity-dot::after {
  right: auto;
  left: 15px; /* Position to the right of the dot with more space */
  text-align: left; /* Align text to the left (closer to the dot) */
  min-width: 10px; /* Ensure consistent spacing */
}

/* Active dot (no zoom) */
.activity-dot.active {
  background-color: var(--accent-primary);
  opacity: 1;
  box-shadow: 0 0 4px rgba(var(--accent-primary-rgb, 30, 144, 255), 0.5);
}

/* Active dot number */
.activity-dot.active::after {
  color: var(--accent-primary);
  opacity: 1;
  font-weight: bold;
}

/* Hover effect for dots (no zoom) */
.activity-dot:hover {
  opacity: 0.8;
}

/* For past activities, position dots on the right side */
.activity-point.past .activity-dots {
  left: auto;
  right: -15px;
}

/* Upcoming activity styles */
.activity-point.upcoming {
  background-color: transparent;
  border: 2px dashed var(--text-primary);
  width: 10px;
  height: 10px;
}

/* Past activity styles */
.activity-point.past {
  opacity: 0.5;
}

.activity-point::after {
  content: '';
  position: absolute;
  width: 24px;
  height: 24px;
  background-color: transparent;
  border: 1px solid var(--text-primary);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.activity-point:hover::after {
  opacity: 1;
}

.activity-date {
  position: absolute;
  top: -35px; /* Moved up slightly for better spacing */
  left: 0; /* Left-aligned with the center of the circle */
  transform: translateX(0);
  font-size: 13px; /* Increased from 11px for better visibility */
  font-weight: bold;
  color: white;
  text-align: left;
  background-color: rgba(80, 80, 80, 0.8); /* Dark grey with transparency */
  padding: 3px 8px;
  border-radius: 12px;
  z-index: 5;
  width: max-content; /* Ensure it takes only the space it needs */
  pointer-events: none; /* Prevent it from blocking hover on the point */
  white-space: nowrap; /* Prevent wrapping */
}

/* Today's activity date - styled like the today marker date */
.activity-point.today-activity-point .activity-date {
  background-color: var(--accent-primary);
  font-weight: bold;
  z-index: 6; /* Higher z-index to appear above other dates */
  left: 50%; /* Center with the circle */
  transform: translateX(-50%); /* Center properly */
  text-align: center; /* Center text */
  top: -45px; /* Move up to make room for the arrow */
}

/* Time difference label */
.time-difference {
  position: absolute;
  top: -54px; /* Same position as Aujourd'hui label */
  left: 0; /* Left-aligned with the center of the circle */
  font-size: 11px;
  color: var(--text-secondary);
  text-align: left;
  font-weight: normal;
  font-style: italic;
  background-color: transparent;
  padding: 0;
  width: auto;
  white-space: nowrap;
  pointer-events: none;
  z-index: 7;
  line-height: 1;
  display: flex;
  align-items: center;
  gap: 3px;
}

/* Today's activity time difference */
.activity-point.today-activity-point .time-difference,
.time-difference.today {
  color: var(--accent-primary);
  font-weight: bold;
  left: 50%; /* Center with the circle */
  transform: translateX(-50%); /* Center properly */
  text-align: center; /* Center text */
  top: -65px; /* Move up to make room for the date label */
}

/* Future time difference */
.time-difference.future {
  color: #4CAF50; /* Green for future dates */
}

/* Past time difference */
.time-difference.past {
  color: #FF5722; /* Orange-red for past dates */
  left: auto; /* Reset left positioning */
  right: 5px; /* Slightly increased margin for better spacing */
  flex-direction: row-reverse; /* Reverse the order of arrow and text */
  gap: 5px; /* Increased gap between arrow and text */
}

/* Upcoming activity date */
.activity-point.upcoming .activity-date {
  background-color: rgba(80, 80, 80, 0.6); /* Lighter background */
  border: 1px dashed rgba(255, 255, 255, 0.5); /* Dashed border */
}

/* Past activity date */
.activity-point.past .activity-date {
  opacity: 0.7;
  left: auto; /* Reset left positioning */
  right: 5px; /* Slightly increased margin for better spacing */
  text-align: right; /* Right-align text */
}

/* Container for multiple activity names */
.activity-names-container {
  position: absolute;
  top: 18px; /* Adjusted for better spacing from the point */
  left: 12px; /* Slightly increased to account for thicker line */
  width: 200px; /* Increased width for better alignment and to prevent overlapping */
  max-width: 200px; /* Ensure the text doesn't exceed this width */
  pointer-events: none; /* Prevent it from blocking hover on the point */
}

/* For multiple activities, adjust the container position to align with dots */
.activity-point.multiple-activities .activity-names-container {
  left: 0; /* Align with the dots */
  margin-left: -5px; /* Small negative margin for better alignment */
}

/* For past activities with multiple activities, adjust the container position */
.activity-point.past.multiple-activities .activity-names-container {
  left: auto;
  right: 0;
  margin-right: -5px; /* Small negative margin for better alignment */
}

.activity-name {
  font-size: 13px; /* Increased from 11px */
  color: var(--text-secondary);
  text-align: left; /* Left-justified text */
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3; /* Show 3 lines instead of 2 */
  -webkit-box-orient: vertical;
  line-height: 1.3; /* Better line spacing */
  margin-bottom: 2px; /* Reduced spacing to make room for lieu */
  pointer-events: none; /* Prevent it from blocking hover on the point */
  font-weight: 600; /* Increased from 500 for better visibility */
}

/* Style for activity lieu */
.activity-lieu {
  font-size: 12px; /* Increased from 10px */
  color: var(--text-secondary);
  opacity: 0.9; /* Increased from 0.8 for better visibility */
  text-align: left;
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* Show 2 lines max */
  -webkit-box-orient: vertical;
  line-height: 1.2;
  margin-bottom: 5px; /* Add spacing between activities */
  pointer-events: none;
  font-style: italic; /* Italic to distinguish from activity name */
}

/* Icon in lieu */
.activity-lieu i {
  margin-right: 3px;
  font-size: 11px; /* Increased from 9px */
}

/* Today's activity name - styled like the today marker activity */
.activity-point.today-activity-point .activity-name {
  color: var(--accent-primary);
  font-weight: bold;
  margin-top: 10px; /* Add top margin from horizontal line */
}

/* Add icon before today's activity name */
.activity-point.today-activity-point .activity-name::before {
  content: '📌'; /* Pin icon */
  margin-right: 5px;
  font-size: 12px;
}

/* Today's activity lieu - styled like the today marker activity */
.activity-point.today-activity-point .activity-lieu {
  color: var(--accent-primary);
  opacity: 0.9;
  font-weight: 500;
}

/* For single today activities, position the lieu */
.activity-point.today-activity-point:not(.multiple-activities) .activity-lieu {
  position: absolute;
  top: 20px; /* Position below the activity name */
  left: 0;
  width: 100%;
  margin-top: 5px; /* Add a bit more space between name and lieu */
}

/* For single activities, maintain the original positioning */
.activity-point:not(.multiple-activities) .activity-name {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

/* For multiple activities, adjust the positioning to align with dots */
.activity-point.multiple-activities .activity-name {
  margin-left: 15px; /* Add margin to align with dots */
  padding-left: 5px; /* Add padding for better spacing */
  border-left: 1px dotted rgba(var(--text-secondary-rgb, 150, 150, 150), 0.3); /* Add subtle dotted line */
}

/* For multiple activities, adjust the lieu positioning to align with dots */
.activity-point.multiple-activities .activity-lieu {
  margin-left: 15px; /* Add margin to align with dots */
  padding-left: 5px; /* Add padding for better spacing */
  border-left: 1px dotted rgba(var(--text-secondary-rgb, 150, 150, 150), 0.3); /* Add subtle dotted line */
}

/* For past activities with multiple activities, adjust the text alignment */
.activity-point.past.multiple-activities .activity-name {
  text-align: right;
  margin-left: 0;
  margin-right: 15px;
  padding-left: 0;
  padding-right: 5px;
  border-left: none;
  border-right: 1px dotted rgba(var(--text-secondary-rgb, 150, 150, 150), 0.3);
}

/* For past activities with multiple activities, adjust the lieu alignment */
.activity-point.past.multiple-activities .activity-lieu {
  text-align: right;
  margin-left: 0;
  margin-right: 15px;
  padding-left: 0;
  padding-right: 5px;
  border-left: none;
  border-right: 1px dotted rgba(var(--text-secondary-rgb, 150, 150, 150), 0.3);
}

/* Style for multiple activities indicator */
.activity-more-indicator {
  font-size: 10px;
  color: var(--accent-primary);
  font-style: italic;
  margin-top: 2px;
  opacity: 0.8;
}

/* Count badge for multiple activities */
.activity-count-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: var(--accent-primary);
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 10px;
  margin-left: 5px;
  font-weight: bold;
}

/* Special styling for today's activity count badge */
.activity-point.today-activity-point .activity-count-badge {
  background-color: white;
  color: var(--accent-primary);
  border: 1px solid var(--accent-primary);
  box-shadow: 0 0 3px rgba(var(--accent-primary-rgb, 30, 144, 255), 0.5);
}

/* Vertical line from circle to activity details */
.activity-vertical-line {
  position: absolute;
  width: 2px; /* Increased stroke width */
  background-color: var(--text-primary);
  top: 0; /* Start from the center of the circle */
  left: 0;
  height: 80px; /* Increased height to accommodate lieu */
  opacity: 0.6; /* Increased opacity for better visibility */
  transform: translateY(6px); /* Adjust to start from center of circle */
  box-shadow: 0 0 1px rgba(var(--text-primary-rgb, 200, 200, 200), 0.3); /* Subtle shadow for better visibility */
}

/* Today's activity vertical line - styled like the today marker vertical line */
.activity-point.today-activity-point .activity-vertical-line {
  background-color: var(--accent-primary);
  opacity: 0.7;
  box-shadow: 0 0 1px rgba(var(--accent-primary-rgb, 30, 144, 255), 0.3);
  height: 90px; /* Increased height to accommodate the top margin and lieu */
}

/* Past activity vertical line */
.activity-point.past .activity-vertical-line {
  opacity: 0.4; /* Slightly increased opacity for past activities */
}

/* Upcoming activity vertical line */
.activity-point.upcoming .activity-vertical-line {
  background-color: transparent;
  border-right: 2px dashed var(--text-primary); /* Increased stroke width */
  width: 0; /* Needed for dashed border to show properly */
  opacity: 0.7; /* Slightly increased opacity for upcoming activities */
}

/* Upcoming activity name */
.activity-point.upcoming .activity-name {
  font-style: italic;
  font-weight: bold;
}

/* Upcoming activity lieu */
.activity-point.upcoming .activity-lieu {
  font-style: italic;
  opacity: 0.7; /* Slightly more transparent for upcoming activities */
}

/* For single upcoming activities, position the lieu */
.activity-point.upcoming:not(.multiple-activities) .activity-lieu {
  position: absolute;
  top: 20px; /* Position below the activity name */
  left: 0;
  width: 100%;
}

/* Past activity names container */
.activity-point.past .activity-names-container {
  opacity: 0.7;
  left: auto; /* Reset left positioning */
  right: 20px; /* Increased margin for better spacing */
  text-align: right; /* Right-align text */
}

/* Past activity name */
.activity-point.past .activity-name {
  text-align: right; /* Right-align text */
}

/* Past activity lieu */
.activity-point.past .activity-lieu {
  text-align: right; /* Right-align text */
}

/* For single past activities, maintain the original positioning */
.activity-point.past:not(.multiple-activities) .activity-name {
  left: auto; /* Reset left positioning */
  right: 20px; /* Increased margin for better spacing */
  width: 200px; /* Increased width for past activities to match future activities */
  max-width: 200px; /* Ensure the text doesn't exceed this width */
}

/* For single past activities, position the lieu */
.activity-point.past:not(.multiple-activities) .activity-lieu {
  position: absolute;
  left: auto; /* Reset left positioning */
  right: 20px; /* Increased margin for better spacing */
  width: 200px; /* Increased width for past activities to match future activities */
  max-width: 200px; /* Ensure the text doesn't exceed this width */
  top: 20px; /* Position below the activity name */
}

/* Past activity more indicator */
.activity-point.past .activity-more-indicator {
  text-align: right;
}

/* Training event styles */
.training-event {
  position: absolute;
  height: 25px;
  top: 2px;
  background-color: transparent; /* Transparent background instead of olive green */
  border-radius: 6px; /* Slightly more rounded corners */
  border: 1px solid rgba(200, 200, 200, 0.5); /* Light grey border */
  color: var(--text-primary); /* Use text primary color instead of white */
  font-size: 10px;
  font-weight: 500; /* Slightly bolder for better visibility */
  padding: 2px 5px;
  overflow: hidden;
  display: flex;
  align-items: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); /* Lighter shadow */
  z-index: 5;
  cursor: pointer;
  transition: all 0.2s ease;
  letter-spacing: 0.2px; /* Slightly increased letter spacing for readability */
}

/* Training name pattern */
.training-name-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  overflow: hidden;
  white-space: nowrap;
  padding: 0 5px;
  /* No animation */
}

/* Training duration label */
.training-duration {
  display: inline-block;
  background-color: rgba(200, 200, 200, 0.25); /* Light grey background */
  border-radius: 10px;
  padding: 1px 6px;
  margin: 0 4px;
  font-size: 9px;
  font-weight: bold;
  white-space: nowrap;
  color: var(--text-primary); /* Use text primary color instead of white */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); /* Lighter shadow */
  line-height: 1.2;
  vertical-align: middle;
  letter-spacing: 0.5px;
}

/* Unified background colors for duration labels based on light grey theme */
.training-event[data-domain="Technique"] .training-duration {
  background-color: rgba(200, 200, 200, 0.3); /* Light grey */
}

.training-event[data-domain="Tactique"] .training-duration {
  background-color: rgba(200, 200, 200, 0.3); /* Light grey */
}

.training-event[data-domain="Opérationnel"] .training-duration {
  background-color: rgba(200, 200, 200, 0.3); /* Light grey */
}

.training-event[data-domain="Administratif"] .training-duration {
  background-color: rgba(200, 200, 200, 0.3); /* Light grey */
  color: var(--text-primary); /* Text primary color for better contrast */
}

/* Default color for other domains */
.training-event[data-domain="Autre"] .training-duration {
  background-color: rgba(200, 200, 200, 0.3); /* Light grey */
}

.training-event:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
  z-index: 10;
}

/* Transparent background for all domains with light grey border */
.training-event[data-domain="Technique"] {
  background: transparent;
  border-color: rgba(200, 200, 200, 0.5);
}

.training-event[data-domain="Tactique"] {
  background: transparent;
  border-color: rgba(200, 200, 200, 0.5);
}

.training-event[data-domain="Opérationnel"] {
  background: transparent;
  border-color: rgba(200, 200, 200, 0.5);
}

.training-event[data-domain="Administratif"] {
  background: transparent;
  border-color: rgba(200, 200, 200, 0.5);
  color: var(--text-primary); /* Use text primary color */
}

/* Default color for other domains */
.training-event[data-domain="Autre"] {
  background: transparent;
  border-color: rgba(200, 200, 200, 0.5);
}

/* Dashboard Styles */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.dashboard-card {
  background-color: var(--bg-secondary);
  border-radius: 6px;
  padding: 20px;
  box-shadow: var(--card-shadow);
  transition: transform 0.3s ease, box-shadow 0.3s ease,
    background-color 0.3s ease, border-color 0.3s ease;
  border: 1px solid var(--border-color);
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
  border-color: var(--accent-primary);
}

.dashboard-card h3 {
  color: var(--accent-primary);
  margin-bottom: 10px;
  transition: color 0.3s ease;
}

/* Map Styles */
.map-container {
  background-color: transparent;
  padding: 0;
  height: 100vh !important; /* Fill the entire window height */
  width: 100vw !important; /* Fill the entire window width */
  position: fixed !important; /* Fixed position to cover the entire window */
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  color: var(--text-primary);
  border: none;
  box-shadow: none !important;
  transition: background-color 0.3s ease, color 0.3s ease;
  overflow: hidden !important; /* Force hidden overflow */
  z-index: 0; /* Set to 0 so other elements can appear above it */
  display: block !important; /* Force display */
  pointer-events: auto !important; /* Ensure map interactions work */
  margin: 0 !important; /* Remove all margins */
  max-width: 100vw !important; /* Prevent horizontal overflow */
  max-height: 100vh !important; /* Prevent vertical overflow */
}

/* Ensure the map element takes full space */
#morocco-map {
  height: 100% !important;
  width: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 0 !important; /* Lower z-index so UI elements appear above */
  display: block !important;
  background-color: transparent !important;
}

/* No city markers needed */

/* Drone marker styles */
.drone-label {
  background: transparent;
  border: none;
}

.drone-label-content {
  padding: 0;
  font-size: 11px; /* Small font size */
  line-height: 1.3;
  white-space: nowrap;
  text-align: left;
  pointer-events: none;
}

/* City name - bold and same color as the circle */
.drone-city {
  font-weight: bold;
  color: #808080; /* Same grey as the circle */
  margin-bottom: 3px;
  font-size: 12px;
}

/* Module info without background */
.drone-info {
  color: var(--text-primary);
  font-size: 10px;
  display: block;
  margin-bottom: 3px;
}

/* Container for drone type on new line */
.drone-type-container {
  display: block;
  margin-top: 2px;
}

/* Only drone type has background */
.drone-type {
  background-color: rgba(255, 255, 255, 0.8);
  color: #333;
  padding: 2px 6px;
  border-radius: 8px;
  display: inline-block;
  font-size: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Dark mode version for drone type */
.dark-theme .drone-type {
  background-color: rgba(50, 50, 50, 0.9);
  color: #eee;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

.drone-popup {
  padding: 5px;
}

.drone-popup h3 {
  margin: 0 0 5px 0;
  color: var(--accent-primary);
  font-size: 14px;
  position: static;
  background: none;
  box-shadow: none;
  border: none;
  text-transform: none;
  letter-spacing: normal;
}

.drone-popup p {
  margin: 0 0 3px 0;
  font-size: 12px;
  color: var(--text-primary);
}







/* Missile Legend */
.missile-legend {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 150px;
}

.dark-theme .missile-legend {
  background: rgba(30, 30, 30, 0.95);
  border-color: rgba(255, 255, 255, 0.2);
}

.legend-title {
  font-size: 12px;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 8px;
  text-align: center;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
}

.legend-item:last-child {
  margin-bottom: 0;
}

.legend-bubble {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.8);
  margin-right: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.legend-text {
  font-size: 11px;
  color: var(--text-primary);
  font-weight: 500;
}

/* Map legend styles removed as requested */

/* Sidi Yahya location pin styles */
.sidi-yahya-pin {
  background: transparent !important;
  border: none !important;
}

.location-pin {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  position: relative;
}

.location-pin i {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* Ammunition icon styles for D.M type */
.ammunition-icon {
  background: transparent !important;
  border: none !important;
}

.ammunition-marker {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  position: relative;
}

.ammunition-marker i {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.4));
}

/* Missions Panel Styles */
.missions-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  bottom: 60px; /* Leave space for footer */
  width: 350px;
  background: rgba(30, 30, 30, 0.9);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  z-index: 1000;
  overflow: visible; /* Allow overflow for selection arrows */
  display: flex;
  flex-direction: column;
}

.light-theme .missions-panel {
  background: rgba(255, 255, 255, 0.9);
}

.missions-header {
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
  background: rgba(0, 0, 0, 0.1);
}

.light-theme .missions-header {
  background: rgba(0, 0, 0, 0.05);
}

.missions-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
}

.missions-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: visible; /* Allow horizontal overflow for selection arrows */
  padding: 10px 10px 10px 25px; /* Add left padding to make room for selection arrows */
  max-height: calc(100vh - 140px); /* Ensure it has a defined height for scrolling */
}

/* Completely hide scrollbar for missions panel and list */
.missions-panel::-webkit-scrollbar,
.missions-list::-webkit-scrollbar {
  display: none !important; /* Completely hide scrollbar */
  width: 0px !important;
  background: transparent !important;
}

.missions-panel::-webkit-scrollbar-track,
.missions-list::-webkit-scrollbar-track {
  display: none !important;
  background: transparent !important;
}

.missions-panel::-webkit-scrollbar-thumb,
.missions-list::-webkit-scrollbar-thumb {
  display: none !important;
  background: transparent !important;
}

.missions-panel::-webkit-scrollbar-thumb:hover,
.missions-list::-webkit-scrollbar-thumb:hover {
  display: none !important;
  background: transparent !important;
}

/* Alternative method - use scrollbar-width for Firefox compatibility */
.missions-panel,
.missions-list {
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* Internet Explorer 10+ */
}

/* Ensure scrollbar is hidden in all themes */
.light-theme .missions-panel::-webkit-scrollbar,
.light-theme .missions-list::-webkit-scrollbar {
  display: none !important;
  width: 0px !important;
  background: transparent !important;
}

.light-theme .missions-panel::-webkit-scrollbar-thumb,
.light-theme .missions-list::-webkit-scrollbar-thumb {
  display: none !important;
  background: transparent !important;
}

.mission-item {
  position: relative;
  padding: 20px;
  margin-bottom: 15px;
  background: rgba(255, 255, 255, 0.08); /* Slightly more opaque for card effect */
  border: 3px solid rgba(255, 255, 255, 0.15); /* Thicker border */
  border-radius: 12px; /* More rounded for card appearance */
  transition: all 0.3s ease;
  min-height: 80px; /* Increased height for card look */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); /* Card shadow */
  backdrop-filter: blur(5px); /* Subtle blur effect */
  cursor: pointer; /* Show it's interactive */
}

.mission-item:hover {
  background: rgba(255, 255, 255, 0.15); /* Brighter on hover */
  border-color: rgba(255, 255, 255, 0.3); /* Brighter border on hover */
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25); /* Enhanced shadow on hover */
  transform: translateY(-2px); /* Slight lift effect */
}

.light-theme .mission-item {
  background: rgba(0, 0, 0, 0.05); /* Slightly more opaque for card effect */
  border: 3px solid rgba(0, 0, 0, 0.15); /* Thicker border for light theme */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); /* Lighter shadow for light theme */
}

.light-theme .mission-item:hover {
  background: rgba(0, 0, 0, 0.1); /* Darker on hover for light theme */
  border-color: rgba(0, 0, 0, 0.25); /* Darker border on hover */
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15); /* Enhanced shadow on hover */
}

/* Selected mission styling */
.mission-item.selected {
  background: rgba(30, 144, 255, 0.3) !important; /* More visible blue background */
  border-color: #1e90ff !important; /* Dodger blue border */
  box-shadow: 0 0 20px rgba(30, 144, 255, 0.5) !important; /* Blue glow */
  position: relative !important;
  transform: translateX(5px) !important; /* Slight right shift to make selection obvious */
}

.light-theme .mission-item.selected {
  background: rgba(30, 144, 255, 0.25) !important; /* More visible blue for light theme */
  border-color: #1e90ff !important; /* Same blue border */
  box-shadow: 0 0 20px rgba(30, 144, 255, 0.3) !important; /* Lighter blue glow */
}

/* Left arrow indicator for selected missions - Using a simpler approach */
.missions-list .mission-item.selected::before {
  content: '◀' !important; /* Unicode left-pointing triangle */
  position: absolute !important;
  left: -20px !important; /* Position outside the mission item */
  top: 50% !important;
  transform: translateY(-50%) !important;
  width: auto !important;
  height: auto !important;
  font-size: 16px !important;
  color: #d3d3d3 !important; /* Light grey for future missions by default */
  z-index: 1001 !important;
  display: block !important;
  line-height: 1 !important;
}

/* Light olive green arrow for current selected missions */
.missions-list .mission-item.selected.current::before {
  color: #9acd32 !important; /* Light olive green for current missions */
}

/* Also handle mission groups */
.missions-list .mission-group .mission-item.selected::before {
  content: '◀' !important; /* Unicode left-pointing triangle */
  position: absolute !important;
  left: -20px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  width: auto !important;
  height: auto !important;
  font-size: 16px !important;
  color: #d3d3d3 !important;
  z-index: 1001 !important;
  display: block !important;
  line-height: 1 !important;
}

.missions-list .mission-group .mission-item.selected.current::before {
  color: #9acd32 !important;
}

/* No current mission placeholder */
.no-current-mission-placeholder {
  padding: 15px 20px;
  margin-bottom: 15px;
  color: #888;
  font-style: italic;
  text-align: center;
  font-size: 14px;
  /* No background, no borders - just text */
}

.light-theme .no-current-mission-placeholder {
  color: #666; /* Slightly darker for light theme */
}

/* Current mission label */
.current-mission-label {
  font-size: 12px;
  color: #00ff00; /* Green color to match current mission styling */
  font-weight: bold;
  margin-top: 5px; /* Margin from date above */
  margin-bottom: 8px; /* Margin to mission type below */
  text-align: left; /* Left aligned with date */
  text-transform: lowercase; /* Lowercase as requested */
  letter-spacing: 0.3px;
}

.light-theme .current-mission-label {
  color: #00aa00; /* Slightly darker green for light theme */
}

/* Mission field styling */
.mission-field {
  font-size: 12px;
  color: #ffffff; /* White color as requested */
  font-style: italic; /* Italic font as requested */
  margin-top: 3px;
  margin-bottom: 5px;
  text-align: left;
  line-height: 1.2;
  display: inline-block;
  margin-right: 10px;
}

.light-theme .mission-field {
  color: #333333; /* Dark color for light theme */
}

/* Location info labels for mission arrows */
.location-info-label {
  pointer-events: none; /* Don't interfere with map interactions */
}

.location-label-content {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
}

.location-label-text {
  background: rgba(0, 0, 0, 0.8); /* Semi-transparent black background */
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px; /* Reduced font size */
  font-weight: 500;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.light-theme .location-label-text {
  background: rgba(255, 255, 255, 0.95); /* Semi-transparent white for light theme */
  color: #333333;
  border: 1px solid rgba(0, 0, 0, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Arrow pointing right (for pickup labels) */
.location-label-arrow.arrow-right {
  width: 0;
  height: 0;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-right: 6px solid rgba(0, 0, 0, 0.8);
}

.light-theme .location-label-arrow.arrow-right {
  border-right-color: rgba(255, 255, 255, 0.95);
}

/* Arrow pointing left (for delivery labels) */
.location-label-arrow.arrow-left {
  width: 0;
  height: 0;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 6px solid rgba(0, 0, 0, 0.8);
}

.light-theme .location-label-arrow.arrow-left {
  border-left-color: rgba(255, 255, 255, 0.95);
}

/* Label positioning */
.pickup-label {
  justify-content: flex-start; /* Label on left, arrow on right pointing to circle */
}

.delivery-label {
  justify-content: flex-end; /* Arrow on left pointing to circle, label on right */
}

/* Dashed borders for future missions are defined below with the .future class */

.mission-item.current {
  border: 3px solid #00ff00 !important; /* Thicker green border for current missions */
  box-shadow: 0 4px 20px rgba(0, 255, 0, 0.4); /* Enhanced glow effect */
}

.mission-item.future {
  opacity: 0.6;
  filter: grayscale(50%);
  border: 3px dashed white !important; /* Thicker white dashed border for future missions in dark theme */
}

.light-theme .mission-item.future {
  border: 3px dashed #999 !important; /* Thicker grey dashed border for future missions in light theme */
}

.mission-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.mission-type {
  background: rgba(255, 0, 0, 0.5); /* Red with 50% transparency */
  color: white;
  padding: 4px 8px;
  border-radius: 4px; /* Reduced border radius */
  font-size: 12px;
  font-weight: 500;
  position: absolute;
  top: 12px;
  right: 12px;
}

.mission-status {
  font-size: 16px;
  margin-left: auto;
}

.mission-status.current {
  color: #00ff00;
  animation: pulse 2s infinite;
}

.mission-status.future {
  color: #888;
}

.mission-date {
  font-size: 11px; /* Smaller font size */
  color: var(--text-color);
  font-weight: 500;
  position: absolute;
  top: 12px;
  left: 12px;
}

.mission-route {
  font-size: 13px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 8px;
  position: absolute;
  bottom: 15px; /* More space from bottom */
  right: 12px;
}

.mission-title {
  font-size: 12px;
  color: #ffffff; /* White color as requested */
  font-style: italic; /* Italic font as requested */
  position: absolute;
  bottom: 15px; /* Same bottom position as mission-route */
  left: 12px; /* Bottom left positioning */
  max-width: 200px; /* Prevent overlap with mission-route */
  line-height: 1.2;
  word-wrap: break-word;
}

.light-theme .mission-title {
  color: #333333; /* Dark color for light theme */
}

.route-separator {
  color: var(--primary-color);
  font-weight: bold;
}

.time-indicator {
  position: absolute;
  top: 50%;
  left: 12px;
  transform: translateY(-50%);
  background: rgba(255, 215, 0, 0.9); /* Gold background */
  color: #000;
  padding: 2px 6px; /* Reduced padding */
  border-radius: 4px; /* Reduced border radius */
  font-size: 9px; /* Reduced font size */
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 6px rgba(255, 215, 0, 0.3);
  border: 1px solid rgba(255, 215, 0, 1);
}

.light-theme .time-indicator {
  background: rgba(255, 193, 7, 0.9); /* Slightly different gold for light theme */
  color: #000;
  box-shadow: 0 2px 6px rgba(255, 193, 7, 0.3);
  border: 1px solid rgba(255, 193, 7, 1);
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
}

/* Round-trip mission styles */
.mission-group {
  margin-bottom: 15px;
  border: 1px solid rgba(var(--primary-color-rgb), 0.3);
  border-radius: 8px;
  overflow: hidden;
}

.mission-group .mission-item {
  margin-bottom: 0;
  border-radius: 0;
  border: none;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2); /* Thin solid border between aller/retour missions */
}

.light-theme .mission-group .mission-item {
  border-bottom: 1px solid rgba(0, 0, 0, 0.2); /* Thin solid border for light theme */
}

.mission-group .mission-item:last-child {
  border-bottom: none;
}

/* Override future mission styling for items inside mission groups */
.mission-group .mission-item.future {
  border: none !important; /* Remove the dashed border for future missions inside groups */
  border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important; /* Keep only the thin solid separator */
}

.light-theme .mission-group .mission-item.future {
  border-bottom: 1px solid rgba(0, 0, 0, 0.2) !important; /* Keep only the thin solid separator for light theme */
}

.mission-group .mission-item.future:last-child {
  border-bottom: none !important; /* Remove border from last item */
}

/* Removed aller/retour arrow indicators as requested */

.mission-group .mission-item {
  padding-left: 25px; /* Make room for arrows */
}

/* Arrow head styles */
.arrow-head {
  background: transparent !important;
  border: none !important;
}

.arrow-head div {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.5));
}

/* Map logo overlay */
.map-logo-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 500;
  opacity: 0.3;
  pointer-events: none; /* Allow clicks to pass through */
  transition: opacity 0.3s ease;
  display: none; /* Hidden by default, shown when no toggle is active */
}

.map-logo-overlay img {
  width: 200px;
  height: auto;
}

/* Map toggle buttons styles */
.map-toggle-container {
  position: fixed;
  left: 80px; /* Positioned to the left, with space for the sidebar */
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 1000;
}

.map-toggle-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 50px; /* Smaller width */
  height: 50px; /* Smaller height */
  border-radius: 50%;
  background-color: var(--bg-secondary);
  border: 2px solid var(--accent-primary);
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  padding: 0;
}

.map-toggle-btn i {
  font-size: 16px; /* Smaller icon */
  margin-bottom: 2px;
  color: var(--accent-primary);
}

.map-toggle-btn span {
  font-size: 8px; /* Smaller text */
  text-align: center;
  line-height: 1;
  max-width: 90%; /* Prevent text overflow */
}

.map-toggle-btn:hover {
  transform: scale(1.1);
  background-color: var(--accent-primary);
  color: white;
}

.map-toggle-btn:hover i {
  color: white;
}

.map-toggle-btn.active {
  background-color: var(--accent-primary);
  color: white;
}

.map-toggle-btn.active i {
  color: white;
}

/* Map reset button styles */
.map-reset-button {
  width: 30px;
  height: 30px;
  border-radius: 4px;
  background-color: transparent;
  border: 1px solid rgba(98, 98, 98, 0.9);
  text-align: center;
  line-height: 28px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  margin-right: 2px;
  transition: all 0.3s ease;
  display: inline-block;
  vertical-align: middle;
  padding: 0;
}

.map-reset-button:hover {
  color: #333;
  border-color: rgba(0, 0, 0, 0.4);
}

.map-reset-button:active {
  transform: translateY(1px);
}

.map-reset-button i {
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  padding: 2px;
  font-size: 12px;
}

/* Map controls container */
.map-controls-container {
  display: flex;
  align-items: center;
  background-color: transparent;
  height: 30px;
  position: absolute;
  bottom: 5px; /* Position at bottom */
  left: 2px; /* Increased left margin */
  z-index: 1000;
}

/* Zoom level display styles */
.zoom-level-display {
  background-color: transparent;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 0 8px;
  height: 30px;
  line-height: 30px;
  font-size: 14px;
  color: #666;
  text-align: center;
  display: inline-block;
  vertical-align: middle;
  min-width: 80px; /* Increased to accommodate the text */
  font-weight: bold;
}

/* Map error message styles */
.map-error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  padding: 20px;
}

.map-error-message {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  max-width: 400px;
  box-shadow: var(--card-shadow);
}

.map-error-message .error-icon {
  font-size: 48px;
  color: #e74c3c;
  margin-bottom: 15px;
}

.map-error-message h3 {
  color: var(--text-primary);
  font-size: 18px;
  margin-bottom: 10px;
  position: static;
}

.map-error-message p {
  color: var(--text-secondary);
  margin-bottom: 10px;
}

.map-error-message .error-details,
.map-error-message .error-path {
  font-size: 12px;
  color: var(--text-secondary);
  margin-top: 5px;
}

.map-error-message .error-details {
  padding-top: 10px;
  border-top: 1px solid var(--border-color);
}

/* Leaflet Map Custom Styling */
.leaflet-container {
  height: 100%;
  width: 100%;
  background-color: transparent !important;
}

/* Style the Leaflet controls to match our theme */
.leaflet-control-zoom a {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

.leaflet-control-zoom a:hover {
  background-color: var(--bg-tertiary) !important;
}

/* Style popups to match our theme */
.leaflet-popup-content-wrapper,
.leaflet-popup-tip {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
  box-shadow: var(--card-shadow) !important;
}

/* Style the attribution control */
.leaflet-control-attribution {
  background-color: rgba(var(--bg-secondary-rgb), 0.7) !important;
  color: var(--text-secondary) !important;
}

/* Style for offline map tiles */
.offline-map-tiles {
  background-color: var(--bg-tertiary) !important;
}

/* Custom popup styling */
.map-popup h3 {
  color: var(--text-primary);
  font-size: 16px;
  margin-bottom: 5px;
  position: static;
}

.map-popup p {
  color: var(--text-secondary);
  font-size: 14px;
  margin: 0;
}

/* List Styles */
.list-container {
  background-color: var(--bg-secondary);
  border-radius: 6px;
  padding: 20px;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
  transition: background-color 0.3s ease, border-color 0.3s ease,
    box-shadow 0.3s ease;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
  transition: border-color 0.3s ease;
}

.data-table th {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.data-table tr:hover {
  background-color: var(--hover-bg);
  transition: background-color 0.3s ease;
}

.action-btn {
  background-color: var(--accent-primary);
  color: #ffffff;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.action-btn:hover {
  background-color: var(--accent-secondary);
}

/* Actions List Styles */
.actions-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow-y: auto !important; /* Allow internal scrolling */
  overflow-x: hidden !important; /* Prevent horizontal scrolling */
  max-height: calc(100vh - 120px) !important; /* Ensure it fits within viewport */
  padding-right: 10px; /* Add padding for scrollbar */
}

.action-item {
  display: flex;
  align-items: center;
  background-color: var(--bg-secondary);
  border-radius: 6px;
  padding: 15px;
  box-shadow: var(--card-shadow);
  transition: transform 0.3s ease, box-shadow 0.3s ease,
    background-color 0.3s ease, border-color 0.3s ease;
  border: 1px solid var(--border-color);
}

.action-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
  border-color: var(--accent-primary);
}

.action-icon {
  width: 50px;
  height: 50px;
  background-color: var(--accent-primary);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
  transition: background-color 0.3s ease;
}

.action-icon i {
  color: #ffffff;
  font-size: 20px;
}

.action-details {
  flex: 1;
}

.action-details h3 {
  color: var(--text-primary);
  margin-bottom: 5px;
  transition: color 0.3s ease;
}

.action-details p {
  color: var(--text-secondary);
  font-size: 14px;
  transition: color 0.3s ease;
}

/* App Footer with Logo and Brand Text */
.app-footer {
  position: fixed;
  bottom: 12px; /* Adjusted from 10px */
  left: 12px; /* Adjusted from 10px */
  display: flex;
  align-items: center;
  z-index: 1000;
}

.brand-container {
  display: flex;
  align-items: center;
  background-color: rgba(30, 30, 30, 0.7); /* Semi-transparent background for dark mode */
  padding: 5px 10px; /* Increased from 4px 8px */
  border-radius: 14px; /* Increased from 12px */
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2); /* Adjusted shadow */
  border: 1px solid rgba(255, 255, 255, 0.1); /* Subtle border for dark mode */
  transition: all 0.3s ease;
  backdrop-filter: blur(5px); /* Add blur effect for modern browsers */
  height: 28px; /* Increased from 22px */
}

/* Light theme specific styling for the footer */
.light-theme .brand-container {
  background-color: white; /* White background for light mode */
  border: 1px solid #e0e0e0; /* Light grey border for light mode */
  backdrop-filter: none; /* No blur effect needed in light mode */
}

.brand-logo {
  height: 20px; /* Increased from 16px */
  width: auto;
  margin-right: 7px; /* Increased from 5px */
}

.brand-separator {
  color: var(--accent-primary); /* Use theme variable */
  margin: 0 7px; /* Increased from 5px */
  font-weight: bold;
  font-size: 12px; /* Increased from 10px */
}

.brand-text {
  color: var(--text-primary); /* Use theme variable */
  font-size: 12px; /* Increased from 10px */
  font-weight: 500;
}

/* Light theme specific styling for the footer text */
.light-theme .brand-text {
  color: #333333; /* Dark grey text for better contrast on white background */
}

.light-theme .brand-separator {
  color: #1e90ff; /* Dodgerblue for light mode */
}

/* Search Bar Styles */
.search-container {
  position: fixed;
  bottom: 12px; /* Adjusted from 10px */
  left: 50%;
  transform: translateX(-50%);
  width: calc(100% - 450px); /* Balanced space for content */
  max-width: 550px; /* Increased from 500px */
  z-index: 1000;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: rgba(30, 30, 30, 0.7);
  border-radius: 6px; /* Increased from 4px */
  padding: 5px 10px; /* Increased padding */
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  min-width: 0; /* Allow container to shrink below content size */
  overflow: hidden; /* Prevent content from overflowing */
  height: 28px; /* Increased from 24px */
}

.light-theme .search-bar {
  background-color: white;
  border: 1px solid #e0e0e0;
  backdrop-filter: none;
}

.search-bar input {
  flex: 1;
  background: transparent;
  border: none;
  color: #aaaaaa; /* Light grey text */
  font-size: 13px; /* Increased from 11px */
  outline: none;
  padding: 4px 0; /* Increased from 2px */
  min-width: 0; /* Allow input to shrink */
  width: 100%; /* Take full width of parent */
  text-overflow: ellipsis; /* Add ellipsis for overflow text */
  line-height: 1.2; /* Increased from 1 */
}

.search-bar input::placeholder {
  color: #888888; /* Light grey placeholder */
}

.search-bar button {
  background: transparent;
  border: none;
  color: #aaaaaa; /* Light grey to match input text */
  cursor: pointer;
  font-size: 12px; /* Increased from 10px */
  margin-left: 5px; /* Increased from 3px */
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0; /* Prevent button from shrinking */
  width: 18px; /* Increased from 14px */
  height: 18px; /* Increased from 14px */
}

.search-bar button:hover {
  color: var(--accent-secondary);
  transform: scale(1.1);
}

/* Bottom Controls (Date Time and Theme Switcher) */
.bottom-controls {
  position: fixed;
  bottom: 12px; /* Adjusted from 10px */
  right: 12px; /* Adjusted from 10px */
  display: flex;
  align-items: center;
  z-index: 1000;
  gap: 10px; /* Increased from 8px */
}

/* Date Time Display */
.datetime-display {
  background-color: rgba(30, 30, 30, 0.7); /* Semi-transparent background for dark mode */
  color: var(--text-primary);
  padding: 5px 10px; /* Increased from 4px 8px */
  border-radius: 14px; /* Increased from 12px */
  font-size: 12px; /* Increased from 10px */
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2); /* Adjusted shadow */
  border: 1px solid rgba(255, 255, 255, 0.1); /* Subtle border for dark mode */
  transition: all 0.3s ease;
  backdrop-filter: blur(5px); /* Add blur effect for modern browsers */
  height: 28px; /* Increased from 22px */
  display: flex;
  align-items: center;
}

/* Light theme specific styling for datetime display */
.light-theme .datetime-display {
  background-color: white; /* White background for light mode */
  color: #333333; /* Dark text for light mode */
  border: 1px solid #e0e0e0; /* Light grey border for light mode */
  backdrop-filter: none; /* No blur effect needed in light mode */
}

/* Fullscreen Toggle and Theme Switcher - Common Styles */
.fullscreen-toggle,
.theme-switcher {
  width: 28px; /* Increased from 22px */
  height: 28px; /* Increased from 22px */
  border-radius: 50%;
  background-color: rgba(30, 30, 30, 0.7); /* Semi-transparent background for dark mode */
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2); /* Adjusted shadow */
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1); /* Subtle border for dark mode */
  backdrop-filter: blur(5px); /* Add blur effect for modern browsers */
}

/* Light theme specific styling for buttons */
.light-theme .fullscreen-toggle,
.light-theme .theme-switcher {
  background-color: white; /* White background for light mode */
  border: 1px solid #e0e0e0; /* Light grey border for light mode */
  backdrop-filter: none; /* No blur effect needed in light mode */
}

.fullscreen-toggle:hover,
.theme-switcher:hover {
  transform: scale(1.1);
}

.fullscreen-toggle i,
.theme-switcher i {
  color: var(--accent-primary);
  font-size: 14px; /* Increased from 10px */
  transition: color 0.3s ease;
}

/* Light theme specific styling for button icons */
.light-theme .fullscreen-toggle i,
.light-theme .theme-switcher i {
  color: #1e90ff; /* Dodgerblue for light mode */
}

/* Responsive Styles */
@media (max-width: 768px) {
  .lateral-tabs {
    width: 60px;
  }

  .lateral-tabs.collapsed {
    width: 0;
    padding: 0;
    left: 0;
  }

  .lateral-tabs.collapsed .tabs-toggle {
    left: 5px;
    width: 25px;
    height: 25px;
  }

  .tab {
    width: 40px;
    height: 40px;
  }

  .tab i {
    font-size: 16px;
  }

  .content-area {
    margin-left: 60px;
    margin-right: 15px; /* Add right margin for symmetry */
    padding: 15px 0 15px 20px; /* Increased left padding */
  }

  .tabs-collapsed .content-area {
    margin-left: 20px; /* Increased left margin for more space */
  }

  .tabs-toggle {
    width: 25px;
    height: 25px;
    right: -12px;
  }

  .tabs-toggle i {
    font-size: 12px;
  }

  h1 {
    top: 5px;
    right: 15px;
    font-size: 10px;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .app-footer {
    bottom: 10px;
    left: 10px;
  }

  .brand-container {
    padding: 6px 10px;
  }

  .brand-logo {
    height: 20px;
  }

  .brand-text {
    font-size: 12px;
  }

  .search-container {
    width: calc(100% - 350px); /* More space for footer items */
    bottom: 10px;
    min-width: 150px; /* Minimum width to ensure usability */
  }

  .search-bar {
    padding: 4px 8px; /* Further reduced padding */
  }

  .search-bar input {
    font-size: 13px; /* Smaller font */
    padding: 4px 0; /* Reduced padding */
  }

  .search-bar button {
    font-size: 13px; /* Smaller icon */
    width: 18px; /* Smaller fixed width */
    height: 18px; /* Smaller fixed height */
    margin-left: 4px; /* Smaller margin */
  }

  .bottom-controls {
    bottom: 10px;
  }

  /* Modal responsive adjustments */
  .modal-content {
    width: 95%;
    margin: 5% auto;
  }

  .detail-row {
    flex-direction: column;
  }

  .detail-label {
    margin-bottom: 4px;
  }
}

.bottom-controls {
  right: 10px;
  gap: 10px;
}

.datetime-display {
    font-size: 12px;
    padding: 6px 10px;
  }

  .fullscreen-toggle,
  .theme-switcher {
    width: 35px;
    height: 35px;
  }

/* For very small screens, adjust the footer */
@media (max-width: 480px) {
  .app-footer {
    bottom: 60px; /* Move above the bottom controls */
  }

  .brand-container {
    max-width: calc(100vw - 20px);
  }

  .brand-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
  }

  .search-container {
    width: calc(100% - 40px);
    bottom: 70px; /* Move higher to avoid overlap */
    left: 20px;
    transform: none;
    min-width: 120px; /* Smaller minimum width for very small screens */
  }

  .search-bar {
    padding: 3px 6px; /* Even smaller for very small screens */
  }

  .search-bar button {
    width: 16px; /* Even smaller fixed width */
    height: 16px; /* Even smaller fixed height */
    margin-left: 3px; /* Even smaller margin */
    font-size: 12px; /* Even smaller icon */
  }

  .bottom-controls {
    width: 100%;
    justify-content: center;
  }
}

/* Activity Details Modal */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.7);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal.show {
  display: block;
  opacity: 1;
}

.modal-content {
  background-color: var(--bg-secondary);
  margin: 10% auto;
  padding: 0;
  border-radius: 8px;
  width: 80%;
  max-width: 600px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  transform: translateY(-20px);
  transition: transform 0.3s ease;
  overflow: hidden;
}

.modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  padding: 15px 20px;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 500;
}

.close-modal {
  color: var(--text-secondary);
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
  transition: color 0.2s;
}

.close-modal:hover {
  color: var(--accent-primary);
}

.modal-body {
  padding: 20px;
}

.activity-nav {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  gap: 8px;
  flex-wrap: wrap;
}

.activity-nav-tab {
  padding: 6px 12px;
  border-radius: 4px;
  background-color: var(--bg-primary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s;
  font-size: 12px;
  text-align: center;
  border: 1px solid transparent;
}

.activity-nav-tab.active {
  background-color: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

.activity-nav-tab:hover:not(.active) {
  background-color: var(--hover-bg);
  border-color: var(--border-color);
}

.activity-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-row {
  display: flex;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  padding-bottom: 8px;
}

.detail-label {
  flex: 0 0 120px;
  font-weight: 500;
  color: var(--text-secondary);
}

.detail-value {
  flex: 1;
  color: var(--text-primary);
}

/* Dashboard Borderpane */
.dashboard-borderpane {
  display: flex;
  flex-direction: column;
  height: auto;
  margin-top: 15px;
  margin-bottom: 40px; /* Ajout d'une marge inférieure de 20px */
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--bg-secondary);
  box-shadow: var(--card-shadow);
  overflow-y: auto !important; /* Allow internal scrolling */
  overflow-x: hidden !important; /* Prevent horizontal scrolling */
  flex: 1;
  max-height: calc(100vh - 120px) !important; /* Ensure it fits within viewport */
}

/* KPI Container Styles */
.kpi-container {
  position: relative;
  padding: 2px;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.kpi-row {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-around;
  gap: 20px;
  width: 100%;
  max-width: 100%; /* Take full width instead of limiting to 1000px */
}

.kpi-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 8px;
  background-color: rgba(var(--bg-tertiary-rgb), 0.5);
  border-radius: 8px;
  transition: background-color 0.3s ease;
  min-width: 100px;
  flex: 1;
  position: relative;
  padding-top: 25px; /* Espace pour l'icône en haut */
}

.kpi-item:hover {
  background-color: rgba(var(--bg-tertiary-rgb), 0.8);
}

.kpi-icon {
  font-size: 14px;
  color: var(--accent-primary);
  opacity: 0.7;
  position: absolute;
  top: 6px;
  left: 6px;
}

.kpi-value {
  font-size: 42px;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 4px;
  line-height: 1;
}

.kpi-label {
  font-size: 12px;
  color: var(--text-secondary);
  text-align: center;
}

.loading-message, .error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--text-secondary);
  text-align: center;
  width: 100%;
  background-color: var(--bg-secondary);
  padding: 20px;
  border-radius: 8px;
  z-index: 10;
}

.error-message {
  color: #ff4d4d;
  border: 1px solid #ff4d4d;
}

/* Styles pour le total des formations et les types de formations */
.kpi-total-trainings {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 15px 8px 8px 8px; /* Augmentation du padding en haut pour les cercles */
  background-color: rgba(var(--bg-tertiary-rgb), 0.5);
  border-radius: 8px;
  transition: background-color 0.3s ease;
  width: 40%; /* Largeur augmentée pour accommoder le graphique */
  max-width: 360px; /* Largeur maximale augmentée */
  height: auto;
  position: relative;
}

/* Conteneur flex pour le graphique et le contenu */
.kpi-container-flex {
  display: flex;
  width: 100%;
  align-items: flex-start;
  justify-content: space-between;
}

/* Section du graphique */
.kpi-chart-section {
  flex: 1;
  position: relative;
  padding-right: 10px;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 50%; /* Largeur fixe */
  min-width: 150px; /* Largeur minimale */
  box-sizing: border-box; /* Pour que le padding ne change pas la largeur */
}

/* Section du contenu */
.kpi-content-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-left: 1px solid var(--border-color);
  padding-left: 10px;
  position: relative;
  width: 50%; /* Largeur fixe */
  min-width: 150px; /* Largeur minimale */
  box-sizing: border-box; /* Pour que le padding ne change pas la largeur */
}

/* Conteneur du graphique de formation */
.training-chart-container {
  position: relative;
  width: 100%;
  height: 60px;
  margin-top: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 150px; /* Largeur minimale fixe */
  box-sizing: border-box; /* Pour que le padding ne change pas la largeur */
}

/* Canvas du graphique de formation */
#training-trend-chart {
  width: 100% !important; /* Force la largeur à 100% du conteneur */
  height: 100% !important; /* Force la hauteur à 100% du conteneur */
}

/* Style pour le message "Données insuffisantes" */
.insufficient-data {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  font-style: italic;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  width: 100%;
}

.kpi-total-trainings .kpi-icon {
  position: absolute;
  top: 6px;
  left: 6px;
  font-size: 14px;
  opacity: 0.7;
}

.kpi-total-trainings .kpi-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  margin-top: 10px;
}

/* Styles pour le toggle de domaine */
.domain-toggle-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 15px;
  margin-bottom: 2px;
  position: relative;
  z-index: 10;
}

/* Style pour les boutons de toggle en dehors de la carte */
.domain-toggle-container.outside-card {
  position: absolute;
  top: -10px;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: auto;
  display: flex;
  justify-content: center;
  z-index: 20;
}

.domain-toggle {
  position: relative;
  display: flex;
  align-items: center;
  background-color: var(--bg-tertiary);
  border-radius: 10px;
  padding: 4px;
  z-index: 10;
  border: 1px solid var(--border-color);
  overflow: visible;
  max-width: 100%;
  justify-content: center;
  height: 14px;
}

/* Style spécifique pour les boutons de toggle en dehors de la carte */
.outside-card .domain-toggle {
  background-color: var(--bg-secondary);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  padding: 5px 10px;
  height: 16px;
}

.domain-toggle-btn {
  width: 12px;
  height: 12px;
  background: var(--bg-tertiary);
  border: 1px solid rgba(200, 200, 200, 0.5); /* Bordure gris clair par défaut */
  cursor: pointer;
  border-radius: 50%;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
  margin: 0 4px;
  padding: 0;
}

/* Style spécifique pour les cercles en dehors de la carte */
.outside-card .domain-toggle-btn {
  width: 14px;
  height: 14px;
  margin: 0 5px;
  border: 1px solid rgba(200, 200, 200, 0.7);
}

.domain-toggle-btn.active {
  border-color: var(--accent-primary);
}

/* Tooltip for domain toggle buttons */
.domain-toggle-btn::after {
  content: attr(data-domain);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
  pointer-events: none;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color);
}

.domain-toggle-btn:hover::after {
  opacity: 1;
  visibility: visible;
}

/* Slider element for domain toggle */
.domain-toggle .toggle-slider {
  position: absolute;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--accent-primary);
  transition: all 0.3s ease;
  z-index: 1;
  top: 50%;
  transform: translateY(-50%);
  left: 4px;
}

/* Style spécifique pour le slider en dehors de la carte */
.outside-card .domain-toggle .toggle-slider {
  width: 14px;
  height: 14px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.kpi-total-trainings .kpi-value {
  font-size: 42px !important;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 4px;
  line-height: 1;
  text-align: center;
}

.kpi-total-trainings .kpi-label {
  font-size: 11px;
  color: var(--text-secondary);
  text-align: center;
  line-height: 1;
}

/* Styles pour les types de formations */
.training-types-container {
  margin-top: 10px;
  width: 100%;
  min-height: 80px; /* Added minimum height for better visibility */
}

.training-type-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px; /* Increased spacing */
  font-size: 12px; /* Slightly larger font */
  padding: 4px 0; /* Added padding for better height */
}

.training-type-name {
  color: var(--text-secondary);
  text-align: left;
}

.training-type-count {
  color: var(--text-primary);
  font-weight: bold;
}

/* Styles pour le graphique d'activités */
.kpi-activity {
  position: relative;
  height: auto;
  min-height: 150px;
  padding-bottom: 15px;
}

.activity-chart-container {
  position: relative;
  width: 100%;
  height: 60px;
  margin-top: 10px;
}

#activity-trend-chart {
  width: 100%;
  height: 100%;
}

.percent-change {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 14px;
  font-weight: bold;
  z-index: 10;
  background-color: rgba(var(--bg-tertiary-rgb), 0.7);
  padding: 2px 6px;
  border-radius: 10px;
}

.percent-change.positive {
  color: #4caf50;
}

.percent-change.negative {
  color: #f44336;
}

.percent-change.neutral {
  color: var(--text-secondary);
}

/* Styles for percent display */
.percent-display {
  font-size: 42px !important;
  font-weight: bold;
}

.percent-display.positive {
  color: #4caf50 !important; /* Green for positive values */
}

.percent-display.negative {
  color: #f44336 !important; /* Red for negative values */
}

.percent-display.neutral {
  color: var(--text-secondary) !important;
}

/* Toggle switch for week/month comparison */
.comparison-toggle {
  position: absolute;
  top: 5px;
  right: 5px;
  display: flex;
  align-items: center;
  background-color: var(--bg-tertiary);
  border-radius: 15px;
  padding: 2px;
  z-index: 10;
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.comparison-toggle-btn {
  padding: 3px 8px;
  font-size: 10px;
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
  flex: 1;
  text-align: center;
}

.comparison-toggle-btn.active {
  color: white;
}

/* Slider element */
.toggle-slider {
  position: absolute;
  top: 2px;
  left: 2px;
  height: calc(100% - 4px);
  width: calc(50% - 2px);
  background-color: var(--accent-primary);
  border-radius: 12px;
  transition: transform 0.3s ease;
  z-index: 1;
}

/* Class to move the slider to the right */
.toggle-slider.right {
  transform: translateX(calc(100% + 4px));
}

/* List View Styles */
.list-container {
  width: 100%;
  overflow-x: auto;
  margin-top: 20px;
}

/* Styles pour les panneaux redimensionnables */
.split-container {
  display: flex;
  width: 100%;
  height: calc(100vh - 150px); /* Utiliser toute la hauteur disponible moins l'espace pour l'en-tête */
  overflow: hidden !important; /* Force hidden overflow to prevent global scrolling */
  margin-top: 20px;
  /* Fond transparent */
  background-color: transparent;
  border-radius: 8px;
  max-height: calc(100vh - 150px) !important; /* Ensure it never exceeds viewport height */
}

.split-panel {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* Fond transparent */
  background-color: transparent;
  border-radius: 4px;
}

.left-panel {
  width: 30%;
  min-width: 200px;
  max-width: 500px; /* Largeur maximale fixe */
  height: 100%; /* Utiliser toute la hauteur disponible */
  transition: width 0.15s ease, min-width 0.15s ease; /* Transitions plus rapides */
  overflow-x: hidden; /* Hide horizontal scrollbar in left panel */
}

.right-panel {
  display: flex;
  flex-direction: column;
  width: 70%;
  min-width: 300px;
  height: 100%; /* Utiliser toute la hauteur disponible */
}

.top-panel {
  height: 50%;
  min-height: 100px;
  max-height: 80%;
}

.bottom-panel {
  height: 50%;
  min-height: 100px;
  max-height: 80%;
}

.split-resizer {
  background-color: var(--border-color);
  position: relative;
  z-index: 10;
}

.vertical-resizer {
  width: 4px;
  height: 100%;
  cursor: col-resize;
}

.horizontal-resizer {
  height: 4px;
  width: 100%;
  cursor: row-resize;
}

.split-resizer:hover, .split-resizer.active {
  background-color: var(--accent-primary);
}

/* Specific styles for the vertical resizer */
#vertical-resizer {
  transition: background-color 0.15s ease, left 0.15s ease, width 0.15s ease;
  z-index: 10; /* Ensure resizer is always visible */
}

/* When left panel is collapsed (width: 0), make resizer more visible */
#vertical-resizer[style*="left: 0px"] {
  background-color: var(--accent-primary) !important;
  width: 6px !important;
  opacity: 0.8;
}

#vertical-resizer[style*="left: 0px"]:hover {
  opacity: 1;
  width: 8px !important;
}

.panel-header {
  padding: 6px 15px;
  background-color: transparent;
  border-bottom: 1px solid var(--border-color);
}

.panel-header h2 {
  margin: 0;
  font-size: 14px;
  color: var(--text-primary);
  font-weight: normal;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Style spécifique pour l'en-tête de formation */
#training-header {
  font-weight: bold;
  color: var(--accent-primary);
  font-size: 18px; /* Taille de police agrandie */
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  gap: 15px;
}

/* Badge pour le nombre total de stagiaires */
.total-trainees-badge {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Styles pour le badge cliquable */
.total-trainees-badge.clickable-badge {
  cursor: pointer;
  transition: all 0.2s ease;
}

.total-trainees-badge.clickable-badge:hover {
  background-color: rgba(var(--accent-primary-rgb), 0.2);
  border-color: var(--accent-primary);
  box-shadow: 0 2px 8px rgba(30, 144, 255, 0.3);
}

.total-trainees-badge.clickable-badge:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(98, 177, 255, 0.2);
}

/* Adaptation pour le thème clair */
body.light-theme .total-trainees-badge {
  background-color: rgba(30, 144, 255, 0.1);
  color: #1E90FF;
  border-color: rgba(30, 144, 255, 0.3);
}

/* Adaptation pour le thème sombre */
body:not(.light-theme) .total-trainees-badge {
  background-color: rgba(30, 144, 255, 0.2);
  color: #1E90FF;
  border-color: rgba(30, 144, 255, 0.4);
}

/* Badge pour la tranche flottante */
.floating-tranche-badge {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid var(--border-color);
  display: none; /* Masqué par défaut */
  align-items: center;
  gap: 5px;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  margin-left: 10px;
}

.floating-tranche-badge.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Adaptation pour le thème clair */
body.light-theme .floating-tranche-badge {
  background-color: rgba(255, 165, 0, 0.1);
  color: #FF8C00;
  border-color: rgba(255, 165, 0, 0.3);
}

/* Badge de comptage des stagiaires */
.trainees-count-badge {
  background-color: var(--accent-primary);
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 600;
  margin-left: 5px;
  display: inline-block;
  min-width: 18px;
  text-align: center;
}

/* Badge pour les stagiaires radiés dans l'onglet */
.trainees-radied-badge {
  background-color: #dc3545;
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 10px;
  font-weight: bold;
  margin-left: 3px;
  min-width: 16px;
  text-align: center;
  display: inline-block;
}

/* Badge pour les stagiaires radiés dans l'en-tête */
.total-radied-badge {
  color: #dc3545;
  font-size: 12px;
  font-weight: bold;
  margin-left: 8px;
}

/* Adaptation pour le thème clair */
body.light-theme .trainees-count-badge {
  color: white;
}

body.light-theme .total-radied-badge {
  color: #b02a37;
}

/* Filtre par fonction */
.fonction-filter {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  outline: none;
  cursor: pointer;
  /* Le filtre sera automatiquement poussé à droite par justify-content: space-between */
}

.fonction-filter:focus {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px rgba(30, 144, 255, 0.2);
}

/* Adaptation pour le thème clair */
body.light-theme .fonction-filter {
  background-color: white;
  border-color: #ddd;
}

body.light-theme .fonction-filter:focus {
  border-color: var(--accent-primary);
}

/* Cellule de fonction avec groupement */
.fonction-cell {
  background-color: var(--bg-tertiary);
  font-weight: 600;
  vertical-align: middle;
  text-align: center;
  border-right: 2px solid var(--border-color);
}

/* Adaptation pour le thème clair */
body.light-theme .fonction-cell {
  background-color: #f8f9fa;
  border-right-color: #dee2e6;
}

/* Ligne de séparation entre les groupes de fonctions */
.fonction-group-separator {
  border-top: 3px solid var(--accent-primary) !important;
}

/* Première ligne d'un groupe de fonction */
.fonction-group-first {
  border-top: 2px solid white; /* Bordure blanche par défaut (thème sombre) */
}

/* Adaptation pour le thème clair */
body.light-theme .fonction-group-first {
  border-top-color: #333; /* Bordure gris foncé pour le thème clair */
}

/* Adaptation pour le thème sombre */
body:not(.light-theme) .floating-tranche-badge {
  background-color: rgba(255, 165, 0, 0.2);
  color: #FFA500;
  border-color: rgba(255, 165, 0, 0.4);
}

/* Styles pour les stagiaires radiés - TEXTE BARRÉ */
.trainee-row.radied {
  background-color: rgba(255, 0, 0, 0.1) !important; /* Arrière-plan rouge léger */
  color: #cc0000 !important; /* Texte rouge */
}

.trainee-row.radied td {
  text-decoration: line-through !important;
  text-decoration-color: #ff0000 !important;
  text-decoration-thickness: 1px !important;
  opacity: 0.5 !important;
}

/* Tooltip pour les stagiaires radiés */
.radied-tooltip {
  position: absolute;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid var(--border-color);
  z-index: 1000;
  max-width: 300px;
  white-space: nowrap;
  pointer-events: none;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
}

.radied-tooltip.show {
  opacity: 1;
  visibility: visible;
}

.radied-tooltip .tooltip-title {
  font-weight: bold;
  color: #ff4444;
  margin-bottom: 4px;
}

.radied-tooltip .tooltip-content {
  color: var(--text-secondary);
  line-height: 1.4;
}

/* Adaptation pour le thème clair */
body.light-theme .trainee-row.radied {
  background-color: rgba(255, 0, 0, 0.15) !important; /* Arrière-plan rouge plus visible pour thème clair */
  color: #990000 !important; /* Texte rouge plus foncé pour thème clair */
}

body.light-theme .trainee-row.radied td {
  text-decoration-color: #cc0000 !important; /* Ligne rouge plus foncée pour thème clair */
}

/* Styles pour la modal de tous les stagiaires */
.all-trainees-modal-content {
  width: 90%;
  max-width: 1200px;
  height: 63vh;
  max-height: 63vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Prevent background scrolling when modal is open */
body.modal-open {
  overflow: hidden;
  position: fixed;
  width: 100%;
}

.modal-search-container {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  align-items: center;
  flex-wrap: wrap;
  flex-shrink: 0;
}

/* Modal body should be scrollable */
.all-trainees-modal-content .modal-body {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 5px;
}

/* Table container should not have its own scroll */
#all-trainees-table-container {
  overflow: visible;
}

/* Modal header should be fixed */
.all-trainees-modal-content .modal-header {
  flex-shrink: 0;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 15px;
  margin-bottom: 0;
}

.search-bar-modal {
  flex: 1;
  min-width: 300px;
  display: flex;
  align-items: center;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 8px 12px;
  transition: border-color 0.2s ease;
}

.search-bar-modal:focus-within {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px rgba(30, 144, 255, 0.2);
}

.search-bar-modal input {
  flex: 1;
  border: none;
  background: transparent;
  color: var(--text-primary);
  font-size: 14px;
  outline: none;
  padding: 0;
}

.search-bar-modal input::placeholder {
  color: var(--text-secondary);
}

.search-bar-modal button {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 3px;
  transition: color 0.2s ease, background-color 0.2s ease;
  margin-left: 8px;
}

.search-bar-modal button:hover {
  color: var(--text-primary);
  background-color: rgba(var(--bg-tertiary-rgb), 0.5);
}

.instance-filter-container {
  flex: 0 0 auto;
  margin-left: 10px;
}

.instance-filter {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 14px;
  min-width: 150px;
}

.fonction-filter-container {
  flex: 0 0 auto;
  margin-left: 10px;
}

.fonction-filter {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 14px;
  min-width: 150px;
}

.all-trainees-table-container {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--bg-secondary);
}

.all-trainees-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--bg-secondary);
}

.all-trainees-table th,
.all-trainees-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
}

.all-trainees-table th {
  background-color: var(--bg-tertiary);
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 10;
}

.all-trainees-table tbody tr:hover {
  background-color: rgba(var(--bg-tertiary-rgb), 0.3);
}

/* Couleurs de fond très légères pour différentes instances */
.all-trainees-table .instance-0 { background-color: rgba(30, 144, 255, 0.03); }
.all-trainees-table .instance-1 { background-color: rgba(255, 99, 132, 0.03); }
.all-trainees-table .instance-2 { background-color: rgba(75, 192, 192, 0.03); }
.all-trainees-table .instance-3 { background-color: rgba(255, 205, 86, 0.03); }
.all-trainees-table .instance-4 { background-color: rgba(153, 102, 255, 0.03); }
.all-trainees-table .instance-5 { background-color: rgba(255, 159, 64, 0.03); }
.all-trainees-table .instance-6 { background-color: rgba(199, 199, 199, 0.03); }
.all-trainees-table .instance-7 { background-color: rgba(83, 102, 255, 0.03); }

/* Styles pour les cellules fusionnées */
.all-trainees-table td:first-child {
  vertical-align: top;
  border-right: 1px solid var(--border-color);
}

.all-trainees-table td:nth-child(2) {
  vertical-align: top;
  border-right: 1px solid var(--border-color);
}

/* Améliorer l'apparence des dates dans la colonne tranche */
.all-trainees-table td:first-child small {
  display: block;
  margin-top: 5px;
  font-size: 11px;
  color: var(--text-secondary);
  line-height: 1.3;
}

/* Styles pour les dates et statuts des instances */
.date-strikethrough {
  text-decoration: line-through;
  color: #999;
}

.extended-date {
  color: var(--accent-primary);
  font-weight: 600;
}

.instance-status {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.instance-status.extended {
  background-color: rgba(255, 193, 7, 0.2);
  color: #856404;
}

.instance-status.rescheduled {
  background-color: rgba(220, 53, 69, 0.2);
  color: #721c24;
}

/* Styles pour les stagiaires radiés dans la modal */
.all-trainees-table .trainee-row.radied {
  background-color: rgba(255, 0, 0, 0.1) !important;
  color: #cc0000 !important;
}

.all-trainees-table .trainee-row.radied td {
  text-decoration: line-through !important;
  text-decoration-color: #ff0000 !important;
  text-decoration-thickness: 1px !important;
  opacity: 0.5 !important;
}

/* Adaptation pour le thème clair */
body.light-theme .search-bar-modal {
  background-color: white;
  border-color: #ddd;
}

body.light-theme .all-trainees-table .trainee-row.radied {
  background-color: rgba(255, 0, 0, 0.15) !important;
  color: #990000 !important;
}

body.light-theme .all-trainees-table .trainee-row.radied td {
  text-decoration-color: #cc0000 !important;
}

/* Style pour le compteur de stagiaires dans le titre de la modal */
.trainee-counter {
  background: var(--accent-primary);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.9em;
  font-weight: 600;
  margin-left: 8px;
  display: inline-block;
  min-width: 20px;
  text-align: center;
}

/* Style pour centrer les messages placeholder */
.placeholder-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
  text-align: center;
  padding: 40px 20px;
}

.placeholder-message i {
  font-size: 48px;
  color: var(--text-secondary);
  margin-bottom: 20px;
  opacity: 0.6;
}

.placeholder-message h3 {
  color: var(--text-secondary);
  margin: 0 0 15px 0;
  font-weight: 500;
  font-size: 18px;
}

.placeholder-message p {
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.5;
  font-size: 14px;
}

/* Styles pour le filtre de domaine */
.domain-filter {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  padding: 5px 0;
}

.domain-button {
  padding: 5px 12px;
  border-radius: 15px;
  border: 1px solid var(--border-color);
  background-color: transparent;
  color: var(--text-secondary);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.domain-button:hover {
  background-color: rgba(var(--bg-secondary-rgb), 0.5);
}

.domain-button.active {
  background-color: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

/* Style pour le compteur de formations */
.domain-count {
  display: none; /* Caché par défaut */
  align-items: center;
  justify-content: center;
  background-color: white;
  color: dodgerblue;
  border-radius: 50%;
  width: 15px;
  height: 15px;
  font-size: 11px;
  font-weight: bold;
  margin-left: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Afficher le compteur uniquement pour le bouton actif */
.domain-button.active .domain-count {
  display: inline-flex;
}

/* Conteneur pour le domaine et son compteur */
.domain-container {
  display: inline-block;
  position: relative;
  margin: 0 5px 5px 0;
}

.panel-content {
  flex: 1;
  overflow: auto;
  padding: 5px 10px;
  max-height: calc(100% - 50px) !important; /* Ensure panel content doesn't exceed panel height */
}

/* Styles pour la liste des formations */
.trainings-list {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden; /* Hide horizontal scrollbar */
}

/* Hide horizontal scrollbar for trainings list */
.trainings-list::-webkit-scrollbar:horizontal {
  display: none;
}

.trainings-list {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: thin; /* Firefox - keep vertical scrollbar thin */
}

.training-item {
  padding: 8px 8px; /* Slightly increased padding for separator space */
  border-bottom: 1px solid rgba(var(--text-primary-rgb), 0.4); /* Updated separator with opacity */
  cursor: pointer;
  transition: all 0.2s ease; /* Smooth transition for all properties */
  margin-bottom: 0; /* Removed margin for separator */
  border-left: 3px solid transparent; /* Invisible border to prevent layout shift */
  word-wrap: break-word; /* Break long words to prevent horizontal overflow */
  overflow-wrap: break-word; /* Modern property for word breaking */
  max-width: 100%; /* Ensure items don't exceed container width */
  box-sizing: border-box; /* Include padding and border in width calculation */
}

.training-item:last-child {
  border-bottom: none; /* Remove separator from last item */
}

.training-item:hover:not(.active) {
  background-color: rgba(var(--bg-secondary-rgb), 0.3); /* Reduced hover effect */
  transform: translateX(1px); /* Slight hover effect */
  transition: all 0.2s ease;
}

.training-item:hover.active {
  background-color: rgba(var(--accent-primary-rgb), 0.25); /* Slightly brighter when active and hovered */
  box-shadow: 0 3px 12px rgba(30, 144, 255, 0.3); /* Enhanced shadow on hover */
}

.training-item.active {
  background-color: rgba(30, 144, 255, 0.15); /* Use direct RGB values for dodgerblue */
  border-left: 3px solid var(--accent-primary); /* Add blue left border */
  border-radius: 4px; /* Add slight border radius */
  box-shadow: 0 2px 8px rgba(30, 144, 255, 0.2); /* Add subtle shadow */
  transform: translateX(2px); /* Slight indent effect */
}

.training-item-name {
  font-weight: bold;
  margin-bottom: 5px;
  word-wrap: break-word; /* Break long training names */
  overflow-wrap: break-word; /* Modern property for word breaking */
  max-width: 100%; /* Ensure names don't exceed container width */
}

.training-item-domain {
  font-size: 11px;
  color: var(--text-primary);
  display: inline-block;
  background-color: rgba(30, 144, 255, 0.15); /* Arrière-plan bleu clair par défaut (thème sombre) */
  border-radius: 4px;
  padding: 2px 6px;
  margin-top: 3px;
  margin-bottom: 3px;
  border: 1px solid rgba(30, 144, 255, 0.3);
  font-weight: 500;
}

/* Adaptation pour le thème clair */
body.light-theme .training-item-domain {
  background-color: rgba(30, 144, 255, 0.25); /* Arrière-plan bleu plus visible pour le thème clair */
  border-color: rgba(30, 144, 255, 0.4);
  color: #333; /* Texte gris foncé pour le thème clair */
}

.training-item-type {
  font-size: 11px;
  color: var(--text-secondary);
  font-style: italic;
}

.training-item-count {
  font-size: 11px;
  font-weight: bold;
  color: var(--accent-primary);
  margin-top: 2px;
}

.bullet-separator {
  margin: 0 5px;
  color: var(--text-secondary);
}

.end-date {
  font-weight: normal;
  color: var(--text-secondary);
  font-style: italic;
}

/* Styles pour les détails de formation */
.training-details {
  height: 100%; /* Utiliser toute la hauteur disponible */
  overflow-y: auto;
  padding: 10px;
  position: relative; /* Add relative positioning for absolute children */
}

/* Specific styles for loading and error messages in training details */
.training-details .loading-message,
.training-details .error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--text-secondary);
  text-align: center;
  width: calc(100% - 40px); /* Account for padding */
  background-color: var(--bg-secondary);
  padding: 30px 20px;
  border-radius: 12px;
  z-index: 10;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
  font-size: 14px;
  font-weight: 500;
}

.training-details .error-message {
  color: #ff4d4d;
  border-color: #ff4d4d;
  background-color: rgba(255, 77, 77, 0.05);
}

/* Specific styles for loading messages in instances list */
.instances-list .loading-message,
.instances-list .error-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: var(--text-secondary);
  text-align: center;
  font-size: 14px;
  font-style: italic;
  background-color: rgba(var(--bg-tertiary-rgb), 0.3);
  border-radius: 8px;
  margin: 10px 0;
}

.instances-list .error-message {
  color: #ff4d4d;
  background-color: rgba(255, 77, 77, 0.05);
  border: 1px solid rgba(255, 77, 77, 0.2);
}

/* Styles pour la liste des instances */
.instances-list-container {
  margin: 20px 0;
  border-top: 1px solid var(--border-color);
  padding-top: 15px;
}

.instances-list-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 2px;
  color: var(--text-primary);
}

.instances-list {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding: 15px 0 15px 0;
  gap: 15px;
  scrollbar-width: thin;
  scrollbar-color: var(--accent-primary) transparent;
}

.instances-list::-webkit-scrollbar {
  height: 1px;
}

.instances-list::-webkit-scrollbar-thumb {
  background-color: var(--accent-primary);
  border-radius: 4px;
}

.instances-list::-webkit-scrollbar-track {
  background-color: transparent;
}

.instance-item {
  flex: 0 0 auto;
  min-width: 200px;
  max-width: 300px;
  padding: 15px;
  padding-bottom: 25px; /* Augmenté pour laisser de la place à la barre de progression */
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: rgba(var(--bg-secondary-rgb), 0.3);
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden; /* Pour que la bordure de progression ne déborde pas */
}

.instance-item:hover {
  background-color: rgba(var(--bg-secondary-rgb), 0.5);
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.instance-item.active {
  border-color: var(--accent-primary);
  background-color: rgba(var(--accent-primary-rgb), 0.1);
}

.instance-title {
  font-weight: bold;
  margin-bottom: 5px;
  color: var(--accent-primary);
}

.instance-dates {
  font-size: 12px;
  color: var(--text-secondary);
}

.instance-status {
  margin-top: 5px;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
  font-weight: bold;
}

.instance-status.rescheduled {
  background-color: rgba(255, 165, 0, 0.2);
  color: orange;
}

.instance-status.extended {
  background-color: rgba(0, 128, 0, 0.2);
  color: green;
}

.instance-status.ongoing {
  background-color: rgba(0, 0, 255, 0.2);
  color: dodgerblue;
  margin-left: 8px;
  font-size: 10px;
  padding: 2px 5px;
  vertical-align: middle;
}

/* Style pour les instances en cours */
.ongoing-instance {
  position: relative;
}

/* Bordure de progression pour les instances en cours */
.progress-border {
  position: absolute;
  bottom: 0;
  left: 0;
  width: var(--progress); /* Largeur basée sur le pourcentage d'avancement */
  height: 4px; /* Hauteur augmentée de la bordure de progression */
  background-color: #4CAF50; /* Nouvelle couleur verte pour la barre de progression */
  transition: width 0.5s ease; /* Animation de la progression */
  z-index: 2; /* Au-dessus du contenu */
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 5px;
  border-radius: 0 8px 8px 0;
}

/* Bordure de progression pour les détails de l'instance */
.instance-info {
  margin-top: 15px;
  line-height: 1.6;
  position: relative;
}

.instance-info.with-progress {
  position: relative;
  overflow: hidden; /* Pour que la bordure de progression ne déborde pas */
  padding-top: 25px; /* Augmenté pour laisser de la place à la barre de progression */
}

.progress-border-details {
  position: absolute;
  top: 0;
  left: 0;
  width: var(--progress); /* Largeur basée sur le pourcentage d'avancement */
  height: 16px; /* Hauteur augmentée de la bordure de progression */
  background-color: #4CAF50; /* Nouvelle couleur verte pour la barre de progression */
  transition: width 0.5s ease; /* Animation de la progression */
  z-index: 2; /* Au-dessus du contenu */
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 5px;
  border-radius: 0 8px 8px 0;
}

/* Style pour le texte du pourcentage dans la barre de progression */
.progress-text {
  font-size: 10px; /* Taille de police minimale */
  font-weight: bold;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.5); /* Ombre légère pour améliorer la lisibilité */
}

/* Texte à l'intérieur de la barre de progression */
.progress-text.inside {
  color: white; /* Texte blanc pour contraster avec le fond vert */
}

/* Texte à l'extérieur de la barre de progression */
.progress-text.outside {
  color: var(--accent-primary); /* Couleur d'accent pour le texte à l'extérieur */
  position: absolute;
  bottom: 5px;
  left: calc(var(--progress) + 5px); /* Positionner juste après la barre de progression */
  z-index: 3;
}

/* Texte à l'extérieur pour les détails */
.progress-text.details-outside {
  top: 5px;
  bottom: auto;
}

.date-strikethrough {
  text-decoration: line-through;
  color: var(--text-secondary);
  opacity: 0.7;
}

.extended-date {
  color: green;
  font-weight: 500;
  margin-top: 2px;
  font-size: 11px;
}

.duration-tag {
  display: inline-block;
  background-color: rgba(var(--bg-tertiary-rgb), 0.5); /* Couleur de fond estompée qui dépend du thème */
  color: var(--text-secondary); /* Couleur de texte qui dépend du thème */
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 2px;
  font-weight: 500;
  vertical-align: middle;
  border: 1px solid rgba(var(--border-color), 0.3); /* Bordure légère qui dépend du thème */
}

/* Style spécifique pour le thème clair */
body.light-theme .duration-tag {
  background-color: rgba(var(--bg-tertiary-rgb), 0.3); /* Plus estompé en mode clair */
  border: 1px solid rgba(var(--border-color), 0.2); /* Bordure plus légère en mode clair */
}

.bullet-separator {
  margin: 0 4px;
  color: var(--text-secondary);
  font-size: 14px;
  vertical-align: middle;
}

.instance-details-container {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid var(--border-color);
  position: relative; /* Pour permettre le positionnement absolu du filtre */
}

.training-additional-info {
  height: 100%;
  overflow-y: auto;
  padding: 15px;
}

.training-additional-info-content h3 {
  font-size: 16px;
  margin-bottom: 15px;
  color: var(--text-primary);
}

.training-additional-info-content div {
  margin-bottom: 10px;
  line-height: 1.5;
}

.training-info {
  margin-bottom: 20px;
  line-height: 1.6;
}

.training-info div {
  margin-bottom: 5px;
}

/* Style pour le titre des détails de formation */
.training-details-header {
  display: flex;
  flex-direction: column;
  margin-bottom: 30px;
  text-align: center;
}

.training-name {
  font-size: 24px;
  color: #000;
  font-weight: bold;
  margin: 20px 0;
}

/* Styles pour les KPIs de formation */
.training-kpis-container {
  margin: 1px 0 15px 0;
  padding: 10px 15px;
  background-color: rgba(var(--bg-secondary-rgb), 0.3);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.kpis-title {
  font-size: 13px;
  margin: 0 0 6px 0;
  color: var(--text-secondary);
  font-weight: normal;
}

.training-kpis {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  justify-content: space-between;
}

.kpi-item {
  flex: 1;
  min-width: 80px;
  text-align: center;
  padding: 8px;
  border-radius: 6px;
  background-color: rgba(var(--bg-primary-rgb), 0.5);
  border: 1px solid var(--border-color);
}

.kpi-value {
  font-size: 18px;
  font-weight: bold;
  color: var(--accent-primary);
  margin-bottom: 4px;
}

.kpi-label {
  font-size: 11px;
  color: var(--text-secondary);
}

/* Styles spécifiques pour chaque type de KPI */
#instances-count-kpi .kpi-value {
  color: var(--accent-primary);
}

#types-kpi .kpi-value {
  color: #4CAF50; /* Vert */
}

#extended-kpi .kpi-value {
  color: #FF9800; /* Orange */
}

#rescheduled-kpi .kpi-value {
  color: #F44336; /* Rouge */
}

.placeholder-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  font-style: italic;
  text-align: center;
  min-height: 200px;
}

/* Specific styles for placeholder messages in training details */
.training-details .placeholder-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: calc(100% - 40px);
  height: auto;
  min-height: 150px;
  background-color: rgba(var(--bg-tertiary-rgb), 0.3);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  padding: 30px 20px;
}

.placeholder-message i {
  font-size: 48px;
  margin-bottom: 20px;
  opacity: 0.6;
  color: var(--accent-primary);
}

.placeholder-message h3 {
  margin: 0 0 10px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  font-style: normal;
}

.placeholder-message p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

/* Styles pour les boutons de bascule dans les détails d'instance */
.instance-toggle-buttons {
  display: flex;
  justify-content: space-between; /* Sépare les boutons à gauche et le filtre à droite */
  align-items: center; /* Aligne verticalement */
  margin-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 10px;
}

/* Groupe des boutons à gauche */
.toggle-buttons-group {
  display: flex;
  gap: 5px;
}

.instance-toggle-btn {
  padding: 8px 15px;
  background-color: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  border-radius: 4px 4px 0 0;
  transition: all 0.2s ease;
  position: relative;
  margin-right: 5px;
}

.instance-toggle-btn:hover {
  color: var(--text-primary);
  background-color: rgba(var(--bg-tertiary-rgb), 0.3);
}

.instance-toggle-btn.active {
  color: var(--accent-primary);
  font-weight: bold;
}

.instance-toggle-btn.active::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--accent-primary);
  border-radius: 3px 3px 0 0;
}

/* Styles pour les contenus des onglets */
.instance-tab-content {
  display: none;
  position: relative; /* Add relative positioning for absolute children */
  min-height: 200px; /* Ensure minimum height for content */
  padding: 20px; /* Add padding for better spacing */
}

.instance-tab-content.active {
  display: block;
}

/* Specific styles for placeholder messages in instance tabs */
.instance-tab-content .placeholder-message {
  position: static; /* Override absolute positioning */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  height: 150px;
  width: 100%;
  background-color: rgba(var(--bg-tertiary-rgb), 0.3);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  margin: 0;
  padding: 30px 20px;
  color: var(--text-secondary);
  font-style: italic;
}

/* Styles pour la table des stagiaires */
.trainees-table-container {
  max-height: calc(100% - 50px) !important;
  height: calc(100% - 50px) !important; /* Fixed height to prevent overflow */
  overflow-y: auto !important;
  overflow-x: hidden !important; /* Prevent horizontal scrolling */
  border: 1px solid grey; /* Bordures blanches par défaut (thème sombre) */
  border-radius: 8px;
}

/* Adaptation pour le thème clair */
body.light-theme .trainees-table-container {
  border-color: #333; /* Bordures gris foncé pour le thème clair */
}

.trainees-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.trainees-table th {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  text-align: left;
  padding: 10px;
  position: sticky;
  top: 0;
  z-index: 10;
  border-bottom: 1px solid white; /* Bordures blanches par défaut (thème sombre) */
}

.trainees-table td {
  padding: 8px 10px;
  border-bottom: 1px solid white; /* Bordures blanches par défaut (thème sombre) */
  color: var(--text-primary);
}

/* Adaptation pour le thème clair */
body.light-theme .trainees-table th {
  border-bottom-color: #333; /* Bordures gris foncé pour le thème clair */
}

body.light-theme .trainees-table td {
  border-bottom-color: #333; /* Bordures gris foncé pour le thème clair */
}

.trainees-table tr:last-child td {
  border-bottom: none;
}

.trainees-table tr:nth-child(even) {
  background-color: rgba(var(--bg-tertiary-rgb), 0.3);
}

.trainees-table tr:hover {
  background-color: rgba(var(--accent-primary), 0.1);
}

/* Message quand aucun stagiaire n'est trouvé */
.no-trainees-message {
  padding: 20px;
  text-align: center;
  color: var(--text-secondary);
  font-style: italic;
}
